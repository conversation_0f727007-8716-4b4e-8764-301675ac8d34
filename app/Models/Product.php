<?php

declare(strict_types=1);

namespace App\Models;

use App\Modules\Product\Models\ProductBrand;
use App\Modules\Product\Models\ProductCategory;
use App\Modules\Product\Models\ProductManufacturer;
use App\Modules\Product\Models\ProductTag;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Support\SequenceNumber;
use App\Support\TestableSearchable;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

final class Product extends Model
{
    use HasFactory, HasUuids, SoftDeletes, TestableSearchable;

    protected $touches = ['searchTerms'];

    protected $table = 'products';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'sku',
        'description',
        'image_url',
        'manufacturer_sku',
        'is_hazardous',
        'requires_prescription',
        'requires_cold_shipping',
        'is_controlled_substance',
        'requires_pedigree',
        'national_drug_code',
    ];

    /**
     * Boot the model.
     */
    public static function boot(): void
    {
        parent::boot();

        self::creating(fn (self $product) => $product->sku = SequenceNumber::next('HF'));
    }

    public function shouldBeSearchable(): bool
    {
        return $this->productOffers()->whereNull('deactivated_at')->count() > 0;
    }

    /**
     * Get the indexable data array for the model.
     */
    public function toSearchableArray(): array
    {
        $now = now();
        $this->load([
            'productOffers.vendor',
            'productOffers.promotions' => function ($query) use ($now) {
                $query->where('status', PromotionStatus::Active)
                    ->where('started_at', '<=', $now)
                    ->where('ended_at', '>=', $now);
            },
            'searchTerms',
            'manufacturer',
        ]);

        $searchTerms = $this->searchTerms->pluck('term')->filter()->unique()->values()->toArray();
        $promoKeywords = $this->getPromotionKeywords();
        $searchTerms = array_values(array_unique(array_merge($searchTerms, $promoKeywords)));

        $activeOffers = $this->productOffers->whereNull('deactivated_at');

        $vendorSkus = $activeOffers->pluck('vendor_sku')->filter()->unique()->values()->toArray();
        $vendorSkusAlphaNum = array_map(fn ($sku) => mb_strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $sku)), $vendorSkus);

        return [
            'id' => $this->id,
            'name' => $this->name,
            'searchTerms' => $searchTerms,
            'manufacturerSku' => $this->manufacturer_sku,
            'vendorSkus' => $vendorSkus,
            'vendorSkusAlphaNum' => $vendorSkusAlphaNum,
            'vendorIds' => $activeOffers->pluck('vendor_id')->filter()->unique()->values()->toArray(),
        ];
    }

    public function searchableConnection(): ?string
    {
        return 'products';
    }

    /**
     * Get the offers for the product.
     */
    public function productOffers(): HasMany
    {
        return $this->hasMany(ProductOffer::class);
    }

    public function manufacturer(): BelongsTo
    {
        return $this->belongsTo(ProductManufacturer::class, 'product_manufacturer_id');
    }

    public function brand(): BelongsTo
    {
        return $this->belongsTo(ProductBrand::class, 'product_brand_id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(ProductCategory::class, 'product_category_id');
    }

    /**
     * Get the attributes for the product.
     */
    public function attributes(): HasMany
    {
        return $this->hasMany(ProductAttribute::class, 'product_id');
    }

    /**
     * Get the search terms associated with this product.
     */
    public function searchTerms(): HasMany
    {
        return $this->hasMany(ProductSearchTerm::class, 'product_id');
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(ProductTag::class, 'product_product_tag', 'product_id', 'product_tag_id');
    }

    /**
     * Collect all unique promotion keywords for this product's offers.
     */
    private function getPromotionKeywords(): array
    {
        $keywords = [];
        foreach ($this->productOffers as $offer) {
            $vendorName = optional($offer->vendor)->name;
            foreach ($offer->promotions as $promotion) {
                $keywords[] = $this->buildPromotionKeyword(
                    $vendorName,
                    $promotion->type->value,
                    $promotion->name
                );
            }
        }

        return array_unique($keywords);
    }

    /**
     * Build a promotion keyword for search indexing.
     */
    private function buildPromotionKeyword($vendor, $type, $name): string
    {
        $parts = array_filter([$vendor, $type, $name]);
        $keyword = implode('-', $parts);
        $keyword = mb_strtolower(str_replace(' ', '-', $keyword));

        return 'promo:'.$keyword;
    }
}
