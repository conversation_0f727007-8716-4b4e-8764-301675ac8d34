<?php

declare(strict_types=1);

namespace App\Models\Modules\Monite;

use App\Models\Clinic;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MoniteUserMapping extends Model
{
    protected $fillable = [
        'user_id',
        'monite_user_id',
        'clinic_id',
        'entity_id',
    ];

    protected $casts = [
        'user_id' => 'string',
        'monite_user_id' => 'string',
        'clinic_id' => 'string',
        'entity_id' => 'string',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    public function scopeForClinic($query, string $clinicId)
    {
        return $query->where('clinic_id', $clinicId);
    }

    public function scopeForUser($query, string $userId)
    {
        return $query->where('user_id', $userId);
    }
}
