<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\AccountRole;
use App\Enums\AddressType;
use App\Enums\ClinicSettingsType;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Clinic\Enums\PracticeType;
use App\Modules\Clinic\Enums\ShoppingPreference;
use App\Modules\Clinic\Enums\SpecieFocus;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Monite\Models\MoniteVendorMapping;
use App\Modules\Order\Models\ImportOrderHistoryTask;
use App\Modules\SavedItems\Models\SavedItem;
use Illuminate\Database\Eloquent\Casts\AsEnumCollection;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laravel\Pennant\Concerns\HasFeatures;

final class Clinic extends Model
{
    use HasFactory, HasFeatures, HasUuids, SoftDeletes;

    protected $guarded = [];

    protected $with = [
        'billingAddress',
        'shippingAddress',
    ];

    public function account(): BelongsTo
    {
        return $this->belongsTo(ClinicAccount::class, 'clinic_account_id');
    }

    /**
     * The monite vendor mappings that belong to the clinic.
     */
    public function moniteVendorMappings(): HasMany
    {
        return $this->hasMany(MoniteVendorMapping::class);
    }

    /**
     * The billing address that belongs to the clinic.
     */
    public function billingAddress(): MorphOne
    {
        return $this->morphOne(Address::class, 'addressable')->where('type', AddressType::Billing);
    }

    /**
     * The shipping address that belongs to the clinic.
     */
    public function shippingAddress(): MorphOne
    {
        return $this->morphOne(Address::class, 'addressable')->where('type', AddressType::Shipping);
    }

    /**
     * Get the budget settings that belong to the clinic.
     */
    public function budgetSettings(): HasOne
    {
        return $this->hasOne(ClinicBudgetSettings::class);
    }

    /**
     * Get the settings that belong to the clinic.
     */
    public function settings(): HasMany
    {
        return $this->hasMany(ClinicSetting::class);
    }

    /**
     * Get the controlled drugs settings that belong to the clinic.
     */
    public function controlledDrugsSettings(): HasOne
    {
        return $this->hasOne(ClinicSetting::class)->where('key', ClinicSettingsType::ControlledDrugs->value);
    }

    /**
     * Get the users that belong to the clinic.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    /**
     * Get the integration connections for the clinic.
     */
    public function integrationConnections(): HasMany
    {
        return $this->hasMany(IntegrationConnection::class, 'clinic_id');
    }

    public function vendors(): HasManyThrough
    {
        return $this->hasManyThrough(
            Vendor::class,
            IntegrationConnection::class,
            'clinic_id',
            'id',
            'id',
            'vendor_id'
        );
    }

    /**
     * Get the cart that belongs to the clinic.
     */
    public function cart(): HasOne
    {
        return $this->hasOne(Cart::class);
    }

    /**
     * Get the orders that belong to the clinic.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the products that belong to the clinic.
     */
    public function productOffers(): BelongsToMany
    {
        return $this->belongsToMany(ProductOffer::class, 'clinic_product_offer', 'clinic_id', 'product_offer_id')
            ->withPivot('price');
    }

    public function favoriteProducts(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'clinic_product_favorites', 'clinic_id', 'product_id')
            ->using(ClinicProductFavorite::class)
            ->withPivot('favorited_at')
            ->withTimestamps();
    }

    /**
     * Add a product to favorites
     */
    public function addFavoriteProduct(Product $product): void
    {
        $this->favoriteProducts()->attach($product->id, [
            'favorited_at' => now(),
        ]);
    }

    /**
     * Remove a product from favorites
     */
    public function removeFavoriteProduct(Product $product): void
    {
        $this->favoriteProducts()->detach($product->id);
    }

    /**
     * Get the favorite products that belong to the clinic.
     */
    public function favoriteProductOffers(): BelongsToMany
    {
        /**
         * This is a OLD relation, it should be removed in the future.
         * We are using only to migrate from old structure to new
         */
        return $this->belongsToMany(ProductOffer::class, 'clinic_product_offer', 'clinic_id', 'product_offer_id')
            ->withPivot('favorited_at')
            ->wherePivot('favorited_at', '!=', null);
    }

    public function importOrderHistoryTasks(): HasMany
    {
        return $this->hasMany(ImportOrderHistoryTask::class);
    }

    /**
     * Get the saved items for this clinic.
     */
    public function savedItems(): HasMany
    {
        return $this->hasMany(SavedItem::class);
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_billing_same_as_shipping_address' => 'boolean',
            'practice_types' => AsEnumCollection::of(PracticeType::class),
            'species_focus' => AsEnumCollection::of(SpecieFocus::class),
            'primary_shopping_preference' => AsEnumCollection::of(ShoppingPreference::class),
            'secondary_shopping_preferences' => AsEnumCollection::of(ShoppingPreference::class),
        ];
    }

    /**
     * Get the managers of the clinic.
     */
    protected function managers(): Attribute
    {
        return new Attribute(
            get: fn () => $this->users->where('role', AccountRole::Manager),
        );
    }

    /**
     * Get the GPO account associated with this clinic.
     */
    protected function gpo(): Attribute
    {
        return new Attribute(
            get: fn () => $this->account?->gpo,
        );
    }
}
