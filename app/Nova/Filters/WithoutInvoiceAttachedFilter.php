<?php

declare(strict_types=1);

namespace App\Nova\Filters;

use Illuminate\Database\Eloquent\Builder;
use <PERSON>vel\Nova\Filters\BooleanFilter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class WithoutInvoiceAttachedFilter extends BooleanFilter
{
    /** @var string */
    public $component = 'boolean-filter';

    public function apply(NovaRequest $request, $query, $value)
    {
        if ($value['without_invoice_attached'] === true) {
            return $query->whereHas('externalOrders', function (Builder $query) {
                $query->whereNull('invoice_file_path');
            });
        }

        return $query;
    }

    public function options(NovaRequest $request)
    {
        return [
            'Without Invoice Attached' => 'without_invoice_attached',
        ];
    }

    public function name()
    {
        return 'Without Invoice Attached';
    }
}
