<?php

declare(strict_types=1);

namespace App\Nova\Actions;

use App\Enums\OrderItemStatus;
use App\Modules\Order\Jobs\Vendor\PlaceOrderToVendor;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use Laravel\Nova\Fields\ActionFields;

final class PlaceVendorSubOrder extends Action
{
    use InteractsWithQueue;
    use Queueable;

    public $name = 'Place Order to Vendor';

    public function handle(ActionFields $fields, Collection $models): ActionResponse
    {
        foreach ($models as $item) {
            if ($item->status === OrderItemStatus::Pending) {
                PlaceOrderToVendor::dispatch($item);
            }
        }

        return ActionResponse::message('Placing order to vendor job dispatched!');
    }
}
