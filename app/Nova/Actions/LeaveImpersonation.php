<?php

declare(strict_types=1);

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Auth;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;

final class LeaveImpersonation extends Action
{
    use InteractsWithQueue;
    use Queueable;

    public $name = 'Leave Impersonation';

    public function handle(): ActionResponse
    {
        Auth::user()->leaveImpersonation();

        // refresh page
        return Action::visit('resources/clinics');
    }
}
