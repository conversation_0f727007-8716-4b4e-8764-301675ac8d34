<?php

declare(strict_types=1);

namespace App\Nova\Actions;

use App\Modules\Order\Actions\ExportOrderCsv as ActionsExportOrderCsv;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use <PERSON>vel\Nova\Fields\ActionFields;

final class ExportOrderCsv extends Action
{
    public function __construct(private ActionsExportOrderCsv $action) {}

    public function handle(ActionFields $fields, Collection $models)
    {
        $order = $models->firstOrFail();

        return ActionResponse::download("{$order->id}-checklist.csv", $order->downloadChecklistUrl);
    }
}
