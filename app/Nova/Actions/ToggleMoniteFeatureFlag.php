<?php

declare(strict_types=1);

namespace App\Nova\Actions;

use App\Models\Clinic;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use Illuminate\Bus\Queueable;
use Illuminate\Http\Request;
use Illuminate\Queue\InteractsWithQueue;
use <PERSON>vel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Http\Requests\NovaRequest;

final class ToggleMoniteFeatureFlag extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Toggle Monite Feature Flag';

    /**
     * Perform the action on the given models.
     */
    public function handle(ActionFields $fields, $models)
    {
        $moniteFeatureService = app(MoniteFeatureFlagService::class);
        $results = [];

        foreach ($models as $clinic) {
            /** @var Clinic $clinic */
            $newStatus = $moniteFeatureService->toggle($clinic);

            $results[] = [
                'clinic' => $clinic->name,
                'status' => $newStatus ? 'enabled' : 'disabled',
            ];
        }

        return Action::message('Monite feature flag toggled successfully');
    }

    /**
     * Get the fields available on the action.
     */
    public function fields(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Determine if the action should be available for the given request.
     */
    public function authorizedToSee(Request $request): bool
    {
        // Available for all users
        return true;
    }

    /**
     * Determine if the user is authorized to perform this action.
     */
    public function authorizedToRun(Request $request, $model): bool
    {
        return true;
    }
}
