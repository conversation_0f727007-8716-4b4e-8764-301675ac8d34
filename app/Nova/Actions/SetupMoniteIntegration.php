<?php

declare(strict_types=1);

namespace App\Nova\Actions;

use App\Models\Clinic;
use App\Modules\Monite\Jobs\CreateOrUpdateMoniteEntityJob;
use App\Modules\Monite\Jobs\SyncAllVendorCounterpartsJob;
use App\Modules\Monite\Jobs\SyncMoniteRolesJob;
use App\Modules\Monite\Jobs\SyncMoniteUsersJob;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use Illuminate\Bus\Queueable;
use Illuminate\Http\Request;
use Illuminate\Queue\InteractsWithQueue;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Http\Requests\NovaRequest;

final class SetupMoniteIntegration extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Setup Monite Integration';

    /**
     * Perform the action on the given models.
     */
    public function handle(ActionFields $fields, $models)
    {
        $moniteFeatureService = app(MoniteFeatureFlagService::class);
        $results = [];

        foreach ($models as $clinic) {
            /** @var Clinic $clinic */

            // Check if Monite feature is enabled for this clinic
            if (! $moniteFeatureService->isEnabled($clinic)) {
                $results[] = [
                    'clinic' => $clinic->name,
                    'status' => 'skipped',
                    'message' => 'Monite feature is disabled for this clinic',
                ];

                continue;
            }

            // Dispatch jobs in a chain to ensure proper execution order
            CreateOrUpdateMoniteEntityJob::withChain([
                new SyncMoniteRolesJob($clinic),
                new SyncMoniteUsersJob($clinic),
                new SyncAllVendorCounterpartsJob($clinic),
            ])->dispatch($clinic);

            $results[] = [
                'clinic' => $clinic->name,
                'status' => 'setup_initiated',
                'message' => 'Monite integration setup jobs dispatched',
            ];
        }

        return Action::message('Monite integration setup initiated successfully');
    }

    /**
     * Get the fields available on the action.
     */
    public function fields(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Determine if the action should be available for the given request.
     */
    public function authorizedToSee(Request $request): bool
    {
        return true;
    }

    /**
     * Determine if the user is authorized to perform this action.
     */
    public function authorizedToRun(Request $request, $model): bool
    {
        return true;
    }
}
