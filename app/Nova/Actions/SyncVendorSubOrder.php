<?php

declare(strict_types=1);

namespace App\Nova\Actions;

use App\Enums\OrderItemStatus;
use App\Modules\Order\Jobs\Vendor\ReconcileOrderFromVendor;
use App\Modules\Order\Jobs\Vendor\SyncInvoiceFromVendor;
use App\Modules\Order\Jobs\Vendor\SyncOrderFromVendor;
use App\Modules\Order\Jobs\Vendor\SyncShipmentFromVendor;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use Laravel\Nova\Fields\ActionFields;

final class SyncVendorSubOrder extends Action
{
    use InteractsWithQueue;
    use Queueable;

    public $name = 'Sync Vendor Order';

    public function handle(ActionFields $fields, Collection $models): mixed
    {
        foreach ($models as $item) {
            if ($item->status !== OrderItemStatus::PlacementFailed) {
                SyncOrderFromVendor::dispatch($item);
                SyncShipmentFromVendor::dispatch($item);
                ReconcileOrderFromVendor::dispatch($item);
                SyncInvoiceFromVendor::dispatch($item);
            }
        }

        return ActionResponse::message('Sync order and shipment from vendor job dispatched!');
    }
}
