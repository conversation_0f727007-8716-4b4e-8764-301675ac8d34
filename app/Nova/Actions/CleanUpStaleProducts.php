<?php

declare(strict_types=1);

namespace App\Nova\Actions;

use App\Modules\Product\Actions\CleanUpStaleProducts as ActionsCleanUpStaleProducts;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use Laravel\Nova\Fields\ActionFields;

final class CleanUpStaleProducts extends Action
{
    use InteractsWithQueue;
    use Queueable;

    public $name = 'Clean Up Stale Products';

    public function __construct(private ActionsCleanUpStaleProducts $action) {}

    public function handle(ActionFields $fields, Collection $models): ActionResponse
    {
        $count = $this->action->handle((int) $fields->days);

        return ActionResponse::message("{$count} stale products have been deactivated");
    }
}
