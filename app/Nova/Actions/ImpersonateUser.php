<?php

declare(strict_types=1);

namespace App\Nova\Actions;

use App\Modules\User\Actions\StartImpersonateUserByClinic;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use Laravel\Nova\Fields\ActionFields;

final class ImpersonateUser extends Action
{
    use InteractsWithQueue;
    use Queueable;

    public $name = 'Impersonate User';

    public function __construct(
        private readonly StartImpersonateUserByClinic $impersonateUserByClinic
    ) {}

    public function handle(ActionFields $fields, Collection $clinics): ActionResponse
    {

        /** @var \App\Models\Clinic $clinic */
        foreach ($clinics as $clinic) {
            $this->impersonateUserByClinic->handle($clinic);
        }

        return Action::redirect(env('FRONTEND_URL').'/clinic-management');
    }
}
