<?php

declare(strict_types=1);

namespace App\Nova\Actions;

use App\Modules\CatalogSync\Jobs\ProcessCatalogSyncBatch;
use App\Modules\CatalogSync\Models\CatalogSyncBatch;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

final class RetryCatalogSyncBatch extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Retry Catalog Sync Batch';

    public function handle(ActionFields $fields, Collection $models): void
    {
        $models->each(fn (CatalogSyncBatch $batch) => ProcessCatalogSyncBatch::dispatch($batch));
    }
}
