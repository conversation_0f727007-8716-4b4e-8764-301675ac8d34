<?php

declare(strict_types=1);

namespace App\Nova\Actions;

use App\Models\Shipment;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Actions\ActionResponse;
use <PERSON>vel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

final class AttachOrderItemShipment extends Action
{
    use InteractsWithQueue;
    use Queueable;

    public $name = 'Attach Order Item Shipment';

    /**
     * Perform the action on the given models.
     */
    public function handle(ActionFields $fields, Collection $models): ActionResponse
    {
        $orderItems = \App\Models\OrderItem::whereKey($models->pluck('id'))->get();

        foreach ($orderItems as $orderItem) {
            $orderItem->shipments()->syncWithoutDetaching($fields['shipments']);
        }

        return ActionResponse::message('Shipments attached successfully');
    }

    /**
     * Get the fields available on the action.
     *
     * @return array<int, \Laravel\Nova\Fields\Field>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            Select::make('Shipments', 'shipments')
                ->searchable()
                ->options(Shipment::where('sub_order_id', $request->viaResourceId)->pluck('tracking_number', 'id'))
                ->displayUsingLabels(),
        ];
    }
}
