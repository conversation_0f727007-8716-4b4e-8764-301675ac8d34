<?php

declare(strict_types=1);

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Currency;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class VendorShippingTerms extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\VendorShippingTerms>
     */
    public static $model = \App\Models\VendorShippingTerms::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'vendor.name';

    /**
     * Disable the ability to create new resources.
     *
     * @var bool
     */
    public static $displayInNavigation = false;

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'vendor.name',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            BelongsTo::make('Vendor', 'vendor', Vendor::class)
                ->readonly()
                ->sortable(),

            Text::make('Cutoff Time', 'cutoff_time'),

            Currency::make('Free Shipping Threshold', 'free_shipping_threshold')
                ->currency('USD')
                ->asMinorUnits()
                ->sortable()
                ->required()
                ->min(0)
                ->step(0.01),

            Currency::make('Shipping Rate', 'shipping_rate')
                ->currency('USD')
                ->asMinorUnits()
                ->sortable()
                ->required()
                ->min(0)
                ->step(0.01),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
