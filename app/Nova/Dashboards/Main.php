<?php

declare(strict_types=1);

namespace App\Nova\Dashboards;

use App\Modules\Account\Nova\Metrics\ClinicAccountsOverTime;
use App\Nova\Metrics\GrossMerchandiseValue;
use App\Nova\Metrics\OrdersOverTime;
use App\Nova\Metrics\OrdersPerDay;
use Laravel\Nova\Dashboards\Main as Dashboard;

final class Main extends Dashboard
{
    /**
     * Get the cards for the dashboard.
     *
     * @return array
     */
    public function cards()
    {
        return [
            (new GrossMerchandiseValue())->width('1/4'),
            (new ClinicAccountsOverTime())->width('1/4'),
            (new OrdersOverTime())->width('1/4'),
            (new OrdersPerDay())->width('1/4'),
        ];
    }
}
