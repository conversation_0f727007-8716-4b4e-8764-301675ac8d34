<?php

declare(strict_types=1);

namespace App\Nova\Metrics;

use App\Models\SubOrder;
use App\Models\Vendor;
use DateInterval;
use DateTimeInterface;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Partition;

final class SubOrdersPerVendor extends Partition
{
    /**
     * Calculate the value of the metric.
     *
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        $vendors = Vendor::all()->pluck('name', 'id');

        $query = SubOrder::with('order.clinic');

        if ($request->resourceId) {
            $query->whereHas('order.clinic', function ($query) use ($request) {
                $query->where('clinic_account_id', $request->resourceId);
            });
        }

        return $this->count($request, $query, 'vendor_id')
            ->label(function ($value) use ($vendors) {
                return $vendors[$value] ?? $value;
            });
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return DateTimeInterface|DateInterval|float|int|null
     */
    public function cacheFor()
    {
        return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'sub-orders-per-vendor';
    }
}
