<?php

declare(strict_types=1);

namespace App\Nova\Fields;

use App\Models\Clinic;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use Laravel\Nova\Fields\Badge;

final class MoniteStatusBadge extends Badge
{
    public function __construct($name, $attribute = null, ?callable $resolveCallback = null)
    {
        parent::__construct($name, $attribute, $resolveCallback);

        $this->resolveUsing(function ($value, $resource, $attribute) {
            /** @var Clinic $resource */
            $moniteFeatureService = app(MoniteFeatureFlagService::class);

            return $moniteFeatureService->isEnabled($resource) ? 'enabled' : 'disabled';
        })->map([
            'enabled' => 'success',
            'disabled' => 'danger',
        ]);
    }
}
