<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Modules\Monite\Exceptions\MoniteApiException;
use App\Modules\Monite\Services\MoniteEntityService;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use App\Modules\Monite\Services\MoniteTokenManager;
use App\Modules\Monite\Services\MoniteUserService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MoniteEntityUserTokenController extends Controller
{
    public function __construct(
        private readonly MoniteTokenManager $tokenManager,
        private readonly MoniteUserService $userService,
        private readonly MoniteEntityService $entityService,
        private readonly MoniteFeatureFlagService $moniteFeatureService
    ) {}

    /**
     * Generate an entity user token for the authenticated user and specified clinic.
     */
    public function store(Request $request): JsonResponse
    {
        $user = $request->user();
        $clinic = $request->clinic();

        // Check if Monite feature is enabled for this clinic
        if (! $this->moniteFeatureService->isEnabled($clinic)) {
            Log::warning('Monite feature disabled for clinic', [
                'user_id' => $user->id,
                'clinic_id' => $clinic->id,
            ]);

            return response()->json([
                'message' => 'Monite integration is not enabled for this clinic.',
                'error' => 'FEATURE_DISABLED',
            ], 403);
        }

        // Check if clinic has been initialized for Monite
        if (! $clinic->monite_entity_id) {
            Log::warning('Clinic not initialized for Monite', [
                'user_id' => $user->id,
                'clinic_id' => $clinic->id,
            ]);

            return response()->json([
                'message' => 'Clinic has not been initialized for Monite.',
                'error' => 'CLINIC_NOT_INITIALIZED',
            ], 400);
        }

        // Check if user has been initialized for Monite
        $moniteUserId = $this->userService->getMoniteUserId($user, $clinic);
        if (! $moniteUserId) {
            Log::warning('User not initialized for Monite', [
                'user_id' => $user->id,
                'clinic_id' => $clinic->id,
            ]);

            return response()->json([
                'message' => 'User not initialized for Monite or does not have permission.',
                'error' => 'USER_NOT_INITIALIZED',
            ], 400);
        }

        try {
            // Generate entity user token
            $accessToken = $this->tokenManager->getEntityUserToken($moniteUserId);

            Log::info('Entity user token generated successfully', [
                'user_id' => $user->id,
                'clinic_id' => $clinic->id,
                'monite_user_id' => $moniteUserId,
                'entity_id' => $clinic->monite_entity_id,
            ]);

            return response()->json([
                'access_token' => $accessToken,
                'token_type' => 'Bearer',
                'expires_in' => 1800, // 30 minutes as per Monite documentation
                'entity_id' => $clinic->monite_entity_id,
                'entity_user_id' => $moniteUserId,
            ]);

        } catch (MoniteApiException $e) {
            Log::error('Monite API error while generating entity user token', [
                'user_id' => $user->id,
                'clinic_id' => $clinic->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'message' => 'Failed to generate entity user token.',
                'error' => $e->getMessage(),
            ], 500);

        } catch (Exception $e) {
            Log::error('Unexpected error while generating entity user token', [
                'user_id' => $user->id,
                'clinic_id' => $clinic->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'message' => 'Failed to generate entity user token.',
            ], 500);
        }
    }
}
