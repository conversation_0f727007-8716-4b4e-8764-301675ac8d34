<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class FeatureFlagController extends Controller
{
    public function __construct(
        private readonly MoniteFeatureFlagService $moniteFeatureService
    ) {}

    public function checkMoniteStatus(Request $request): JsonResponse
    {
        $clinic = $request->clinic();

        $isEnabled = $this->moniteFeatureService->isEnabled($clinic);

        return response()->json([
            'clinic_id' => $clinic->id,
            'clinic_name' => $clinic->name,
            'monite_enabled' => $isEnabled,
        ]);
    }
}
