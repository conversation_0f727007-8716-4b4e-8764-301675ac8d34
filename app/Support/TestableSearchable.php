<?php

declare(strict_types=1);

namespace App\Support;

use Elastic\ScoutDriverPlus\Searchable;
use Illuminate\Support\Facades\App;
use <PERSON><PERSON>\Scout\EngineManager;
use <PERSON><PERSON>\Scout\Engines\CollectionEngine;

trait TestableSearchable
{
    use Searchable;

    /**
     * Get the engine used to index the model.
     */
    public function searchableUsing()
    {
        $engine = app(EngineManager::class)->engine();

        // If we're in testing environment with CollectionEngine, skip connection method
        if (App::environment('testing') && $engine instanceof CollectionEngine) {
            return $engine;
        }

        $connection = $this->searchableConnection();

        return isset($connection) ? $engine->connection($connection) : $engine;
    }
}
