<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Services;

use App\Models\Clinic;
use App\Models\Order;
use App\Models\User;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\User\Enums\UserRole;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
use Rap2hpoutre\FastExcel\FastExcel;
use Rap2hpoutre\FastExcel\SheetCollection;

final class PlatformUsageExportService
{
    /**
     * Export platform usage data to an XLSX file.
     *
     * @param  Collection<int, Clinic>  $clinics
     */
    public function exportToXlsx(Collection $clinics, Carbon $startDate, Carbon $endDate, int $activeDaysThreshold)
    {
        $fileName = $this->generateFileName($startDate, $endDate);
        $filePath = Storage::path($fileName);

        $clinicData = $this->prepareClinicData($clinics, $startDate, $endDate, $activeDaysThreshold);
        $userData = $this->prepareUserData($clinics, $startDate, $endDate);

        $sheets = new SheetCollection([
            'Clinic Overview' => $clinicData,
            'User overview' => $userData,
        ]);
        (new FastExcel($sheets))->export($filePath);

        return Response::download($filePath, $fileName)->deleteFileAfterSend(true);
    }

    private function generateFileName(Carbon $startDate, Carbon $endDate): string
    {
        return 'platform_usage_'.$startDate->format('Y-m-d').'_to_'.$endDate->format('Y-m-d').'_'.now()->format('Y-m-d_H-i-s').'.xlsx';
    }

    private function prepareClinicData(Collection $clinics, Carbon $startDate, Carbon $endDate, int $activeDaysThreshold): array
    {
        $data = [];

        // Process data in chunks to avoid memory issues
        $clinics->chunk(100)->each(function (Collection $clinicChunk) use (&$data, $startDate, $endDate, $activeDaysThreshold) {
            foreach ($clinicChunk as $clinic) {
                $data[] = $this->prepareClinicRow($clinic, $startDate, $endDate, $activeDaysThreshold);
            }
        });

        return $data;
    }

    private function prepareClinicRow(Clinic $clinic, Carbon $startDate, Carbon $endDate, int $activeDaysThreshold): array
    {
        // Load necessary relationships
        $clinic->loadMissing(['account.gpo', 'users', 'orders']);

        $clinicData = $this->calculateClinicMetrics($clinic, $startDate, $endDate, $activeDaysThreshold);

        return [
            'Clinic Name' => $clinic->name ?? 'N/A',
            'Unique Users' => $clinicData['unique_users'],
            'Last Active Date' => $clinicData['last_active_date'],
            'Average Order Total ($)' => $clinicData['average_order_total'],
            'Avg # All Vendors' => $clinicData['avg_all_vendors'],
            'Avg # Preferred Vendors' => $clinicData['avg_preferred_vendors'],
            'Average Session Time (min)' => $clinicData['average_session_time'],
            'Status' => $clinicData['status'],
        ];
    }

    // TODO: Implement session time logging
    private function calculateAverageSessionTime(Clinic $clinic, Carbon $startDate, Carbon $endDate): string
    {
        return '0.0';
    }

    private function calculateClinicMetrics(Clinic $clinic, Carbon $startDate, Carbon $endDate, int $activeDaysThreshold): array
    {
        // Get unique users count
        $uniqueUsers = $clinic->users()->count();

        // Get last active date (most recent order)
        $lastOrder = $clinic->orders()
            ->whereNull('import_order_history_task_id')
            ->orderBy('created_at', 'desc')
            ->first();

        $lastActiveDate = $lastOrder ? $lastOrder->created_at->format('Y-m-d') : 'Never';

        // Get orders within date range for calculations
        $orders = $clinic->orders()
            ->whereNull('import_order_history_task_id')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with(['items.productOffer.vendor'])
            ->get();

        // Calculate average order total
        $averageOrderTotal = $orders->count() > 0
            ? round($orders->sum('total_price') / 100 / $orders->count(), 2) // Convert cents to dollars
            : 0.00;

        // Calculate average number of vendors per order
        $vendorCounts = $orders->map(function ($order) {
            return $order->items->pluck('productOffer.vendor.id')->unique()->count();
        });

        $avgAllVendors = $vendorCounts->count() > 0
            ? round($vendorCounts->average(), 1)
            : 0;

        // Calculate average number of preferred vendors per order
        $gpo = $clinic->account?->gpo;
        $recommendedVendorIds = $gpo
            ? $gpo->recommendedVendors()->pluck('vendor_id')->toArray()
            : [];

        $preferredVendorCounts = $orders->map(function ($order) use ($recommendedVendorIds) {
            return $order->items
                ->pluck('productOffer.vendor.id')
                ->unique()
                ->intersect($recommendedVendorIds)
                ->count();
        });

        $avgPreferredVendors = $preferredVendorCounts->count() > 0
            ? round($preferredVendorCounts->average(), 1)
            : 0;

        // Calculate average session time (simplified - based on user activity)
        $averageSessionTime = $this->calculateAverageSessionTime($clinic, $startDate, $endDate);

        // Determine status based on activity threshold
        $thresholdDate = Carbon::now()->subDays($activeDaysThreshold);
        $isActive = $lastOrder && $lastOrder->created_at >= $thresholdDate;
        $status = $isActive ? 'Active' : 'Inactive';

        return [
            'unique_users' => $uniqueUsers,
            'last_active_date' => $lastActiveDate,
            'average_order_total' => number_format($averageOrderTotal, 2),
            'avg_all_vendors' => $avgAllVendors,
            'avg_preferred_vendors' => $avgPreferredVendors,
            'average_session_time' => $averageSessionTime,
            'status' => $status,
        ];
    }

    private function prepareUserData(Collection $clinics, Carbon $startDate, Carbon $endDate): array
    {
        $data = [];

        // Process data in chunks to avoid memory issues
        $clinics->chunk(100)->each(function (Collection $clinicChunk) use (&$data, $startDate, $endDate) {
            foreach ($clinicChunk as $clinic) {
                // Load users relationship
                $clinic->loadMissing(['users']);

                // Create a row for each user in the clinic
                foreach ($clinic->users as $user) {
                    $data[] = $this->prepareUserDataRow($clinic, $user, $startDate, $endDate);
                }
            }
        });

        return $data;
    }

    private function prepareUserDataRow(Clinic $clinic, User $user, Carbon $startDate, Carbon $endDate): array
    {
        $userMetrics = $this->calculateUserMetrics($clinic, $user, $startDate, $endDate);

        return [
            'Clinic Name' => $clinic->name ?? 'N/A',
            'User Name' => $user->name ?? 'N/A',
            'Access Level' => $this->getUserAccessLevel($user),
            'Average Session Time (min)' => $userMetrics['average_session_time'],
            'Average Order Total ($)' => $userMetrics['average_order_total'],
            'Avg # All Vendors' => $userMetrics['avg_all_vendors'],
            'Avg # Preferred Vendors' => $userMetrics['avg_preferred_vendors'],
        ];
    }

    private function getUserAccessLevel(User $user): string
    {
        // Get user's roles - using Spatie Permission package
        $roles = $user->roles->pluck('name');

        if ($roles->isEmpty()) {
            return 'N/A';
        }

        // Map UserRole enum values to display names based on the example data
        $roleMapping = [
            ClinicAccountRole::Owner->value => 'Owner',
            ClinicAccountRole::Admin->value => 'Admin',
            ClinicAccountRole::Manager->value => 'Manager',
            UserRole::Owner->value => 'Owner/Admin',
            UserRole::Executive->value => 'Executive',
            UserRole::RegionalManager->value => 'Regional/Hospital Manager',
            UserRole::ClinicManager->value => 'Clinic Manager',
            UserRole::InventoryManager->value => 'Inventory Manager',
            UserRole::Purchaser->value => 'Purchaser',
            UserRole::BookKeeper->value => 'BookKeeper',
        ];

        // Get the first role and map it
        $firstRole = $roles->first();

        return $roleMapping[$firstRole] ?? $firstRole;
    }

    private function calculateUserAverageSessionTime(User $user, Carbon $startDate, Carbon $endDate): string
    {
        // For now, return a placeholder since we don't have session start times
        // In a real implementation, you'd need to track session start/end times
        // or calculate based on activity patterns
        return '0.0';
    }

    private function calculateUserMetrics(Clinic $clinic, User $user, Carbon $startDate, Carbon $endDate): array
    {
        // Get user's orders within the date range for this clinic
        $orders = $clinic->orders()
            ->where('user_id', $user->id)
            ->whereNull('import_order_history_task_id')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with(['items.productOffer.vendor'])
            ->get();

        // Calculate average order total
        $averageOrderTotal = $orders->count() > 0
            ? round($orders->sum('total_price') / 100 / $orders->count(), 2) // Convert cents to dollars
            : 0.00;

        // Calculate average number of vendors per order
        $vendorCounts = $orders->map(function ($order) {
            return $order->items->pluck('productOffer.vendor.id')->unique()->count();
        });

        $avgAllVendors = $vendorCounts->count() > 0
            ? round($vendorCounts->average(), 0)
            : 0;

        // Calculate average number of preferred vendors per order
        $gpo = $clinic->account?->gpo;
        $recommendedVendorIds = $gpo
            ? $gpo->recommendedVendors()->pluck('vendor_id')->toArray()
            : [];

        $preferredVendorCounts = $orders->map(function ($order) use ($recommendedVendorIds) {
            return $order->items
                ->pluck('productOffer.vendor.id')
                ->unique()
                ->intersect($recommendedVendorIds)
                ->count();
        });

        $avgPreferredVendors = $preferredVendorCounts->count() > 0
            ? round($preferredVendorCounts->average(), 0)
            : 0;

        // Calculate average session time
        $averageSessionTime = $this->calculateUserAverageSessionTime($user, $startDate, $endDate);

        return [
            'average_order_total' => number_format($averageOrderTotal, 2),
            'avg_all_vendors' => $avgAllVendors,
            'avg_preferred_vendors' => $avgPreferredVendors,
            'average_session_time' => $averageSessionTime,
        ];
    }
}
