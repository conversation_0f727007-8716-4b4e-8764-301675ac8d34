<?php

declare(strict_types=1);

namespace App\Modules\Product\Nova;

use App\Nova\Product;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Resource;

final class ProductTag extends Resource
{
    public static $model = \App\Modules\Product\Models\ProductTag::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')
                ->sortable()
                ->rules('required', 'max:255', 'unique:product_tags,name'),

            HasMany::make('Products', 'products', Product::class),
        ];
    }
}
