<?php

declare(strict_types=1);

namespace App\Modules\Product\Nova;

use App\Nova\Product;
use Laravel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Resource;

final class ProductCategory extends Resource
{
    public static $model = \App\Modules\Product\Models\ProductCategory::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public function subtitle()
    {
        return $this->resource->getFullPath(' > ');
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Parent', 'parent', self::class)
                ->nullable()
                ->searchable(),

            Text::make('Name')
                ->sortable()
                ->rules('required', 'max:255'),

            HasMany::make('Children', 'children', self::class),

            HasMany::make('Products', 'products', Product::class),
        ];
    }
}
