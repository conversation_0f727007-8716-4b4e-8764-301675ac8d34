<?php

declare(strict_types=1);

namespace App\Modules\Product\Models;

use App\Models\Product;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class ProductCategory extends Model
{
    use HasUuids;

    protected $guarded = [];

    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'product_category_id');
    }

    public function getFullPath($separator = ' > '): string
    {
        $segments = [];
        $current = $this;
        $visited = [];

        while ($current instanceof self) {
            // prevent accidental cycles
            $id = $current->getKey();
            if ($id !== null) {
                if (isset($visited[$id])) {
                    break;
                }
                $visited[$id] = true;
            }

            // assuming there's a 'name' column
            $name = (string) $current->getAttribute('name');
            if ($name !== '') {
                $segments[] = $name;
            }

            // if 'parent' was eager-loaded, reuse it; otherwise lazy-load once
            $current = $current->relationLoaded('parent')
                ? $current->getRelation('parent')
                : $current->parent;
        }

        return implode($separator, array_reverse($segments));
    }
}
