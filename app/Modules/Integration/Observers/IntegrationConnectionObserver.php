<?php

declare(strict_types=1);

namespace App\Modules\Integration\Observers;

use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Mails\NotifyIntegrationDisconnected;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Support\Facades\Mail;

final class IntegrationConnectionObserver
{
    public function updated(IntegrationConnection $integrationConnection): void
    {
        if ($integrationConnection->status === IntegrationConnectionStatus::Disconnected) {
            $users = $integrationConnection->clinic->account->users;

            if ($users->isEmpty()) {
                return;
            }

            Mail::to('<EMAIL>')->queue(new NotifyIntegrationDisconnected($integrationConnection));
            /*foreach ($users as $user) {
                Mail::to($user->email)->queue(new NotifyIntegrationDisconnected($integrationConnection));
            }*/
        }
    }
}
