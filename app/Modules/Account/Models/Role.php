<?php

declare(strict_types=1);

namespace App\Modules\Account\Models;

use App\Modules\Account\Models\Factories\RoleFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Permission\Models\Role as SpatieRole;

final class Role extends SpatieRole
{
    use HasFactory;
    use HasUuids;

    protected static function newFactory(): RoleFactory
    {
        return RoleFactory::new();
    }
}
