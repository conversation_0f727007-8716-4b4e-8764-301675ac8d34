<?php

declare(strict_types=1);

namespace App\Modules\Account\Models\Factories;

use App\Modules\Account\Models\Role;
use Illuminate\Database\Eloquent\Factories\Factory;

final class RoleFactory extends Factory
{
    protected $model = Role::class;

    public function definition(): array
    {
        return [
            'name' => fake()->randomElement(['clinic:owner', 'clinic:admin', 'clinic:manager']),
            'guard_name' => 'web',
        ];
    }

    public function owner(): static
    {
        return $this->state(['name' => 'clinic:owner']);
    }

    public function admin(): static
    {
        return $this->state(['name' => 'clinic:admin']);
    }

    public function manager(): static
    {
        return $this->state(['name' => 'clinic:manager']);
    }
}
