<?php

declare(strict_types=1);

namespace App\Modules\Monite\Actions;

use App\Models\Clinic;
use App\Modules\Monite\Services\MoniteEntityService;
use Exception;
use Illuminate\Support\Facades\Log;

final class DeleteEntityAction
{
    public function __construct(
        private readonly MoniteEntityService $moniteEntityService
    ) {}

    public function execute(Clinic $clinic): bool
    {
        if (! $clinic->monite_entity_id) {
            return true; // Nothing to delete
        }

        try {
            $this->moniteEntityService->deleteEntityForClinic($clinic);
            $clinic->update(['monite_entity_id' => null]);

            Log::info('Monite entity deleted for clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('Failed to delete Monite entity for clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'monite_entity_id' => $clinic->monite_entity_id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }
}
