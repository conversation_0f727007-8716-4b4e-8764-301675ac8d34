<?php

declare(strict_types=1);

namespace App\Modules\Monite\Actions;

use App\Models\Clinic;
use App\Modules\Monite\Jobs\SyncMoniteRolesJob;
use App\Modules\Monite\Jobs\SyncMoniteUsersJob;
use App\Modules\Monite\Services\MoniteEntityService;
use Exception;
use Illuminate\Support\Facades\Log;

final class CreateOrUpdateEntityAction
{
    public function __construct(
        private readonly MoniteEntityService $moniteEntityService
    ) {}

    public function execute(Clinic $clinic): ?string
    {
        try {
            if (! $clinic->monite_entity_id) {
                // Create new entity
                $entityId = $this->moniteEntityService->createEntityForClinic($clinic);

                // If feature is disabled, createEntityForClinic returns null
                if ($entityId === null) {
                    return null;
                }

                $clinic->update(['monite_entity_id' => $entityId]);

                Log::info('Monite entity created for clinic', [
                    'clinic_id' => $clinic->id,
                    'clinic_name' => $clinic->name,
                    'monite_entity_id' => $entityId,
                ]);

                // After creating the entity, create the roles and users
                $this->createRolesForClinic($clinic);
                $this->createUsersForClinic($clinic);

                return $entityId;
            }
            // Update existing entity
            $this->moniteEntityService->updateEntityForClinic($clinic);

            Log::info('Monite entity updated for clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'monite_entity_id' => $clinic->monite_entity_id,
            ]);

            return $clinic->monite_entity_id;

        } catch (Exception $e) {
            Log::error('Failed to create/update Monite entity for clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'monite_entity_id' => $clinic->monite_entity_id,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Create Monite roles for the clinic after entity creation
     */
    private function createRolesForClinic(Clinic $clinic): void
    {
        // Dispatch the role sync job - no try-catch needed as jobs are queueable
        SyncMoniteRolesJob::dispatch($clinic);

        Log::info('Monite role sync job dispatched for clinic', [
            'clinic_id' => $clinic->id,
            'clinic_name' => $clinic->name,
            'monite_entity_id' => $clinic->monite_entity_id,
        ]);
    }

    /**
     * Create Monite users for the clinic after entity creation
     */
    private function createUsersForClinic(Clinic $clinic): void
    {
        // Dispatch the user sync job - no try-catch needed as jobs are queueable
        SyncMoniteUsersJob::dispatch($clinic);

        Log::info('Monite user sync job dispatched for clinic', [
            'clinic_id' => $clinic->id,
            'clinic_name' => $clinic->name,
            'monite_entity_id' => $clinic->monite_entity_id,
        ]);
    }
}
