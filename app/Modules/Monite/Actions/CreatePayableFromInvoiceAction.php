<?php

declare(strict_types=1);

namespace App\Modules\Monite\Actions;

use App\Models\Clinic;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Exceptions\MoniteApiException;
use App\Modules\Order\Models\ExternalOrder;
use App\Modules\Order\Services\Vendor\Contracts\InvoiceSynchronizer;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Throwable;

final class CreatePayableFromInvoiceAction
{
    public function __construct(
        private readonly MoniteApiClientInterface $apiClient
    ) {}

    /**
     * Create a payable in Monite from an invoice file
     */
    public function execute(ExternalOrder $externalOrder, Clinic $clinic): ?string
    {
        // Check if invoice file exists
        if (empty($externalOrder->invoice_file_path)) {
            Log::warning('Cannot create Monite payable: Invoice file path is empty', [
                'external_order_id' => $externalOrder->id,
            ]);

            return null;
        }

        // Skip if already processed
        if ($externalOrder->monite_payable_id) {
            Log::info('Monite payable already exists for this invoice', [
                'external_order_id' => $externalOrder->id,
                'monite_payable_id' => $externalOrder->monite_payable_id,
            ]);

            return $externalOrder->monite_payable_id;
        }

        try {
            // Get the invoice file content
            $fileContent = $this->getInvoiceFileContent($externalOrder);
            if (! $fileContent) {
                Log::error('Cannot read invoice file', [
                    'external_order_id' => $externalOrder->id,
                    'invoice_file_path' => $externalOrder->invoice_file_path,
                ]);

                return null;
            }

            // Get file info
            $fileName = basename($externalOrder->invoice_file_path);
            $tempFilePath = sys_get_temp_dir().'/'.$fileName;

            // Write the content to a temporary file
            file_put_contents($tempFilePath, $fileContent);

            // Create the payable via Monite API
            $response = $this->uploadFileToMonite($tempFilePath, $clinic);

            // Clean up the temporary file
            if (file_exists($tempFilePath)) {
                unlink($tempFilePath);
            }

            // Extract payable ID from response
            $payableId = $response['id'] ?? null;
            if (! $payableId) {
                Log::error('Failed to get payable ID from Monite response', [
                    'external_order_id' => $externalOrder->id,
                    'response' => $response,
                ]);

                return null;
            }

            // Update the external order with the payable ID
            $externalOrder->update([
                'monite_payable_id' => $payableId,
            ]);

            Log::info('Successfully created Monite payable', [
                'external_order_id' => $externalOrder->id,
                'monite_payable_id' => $payableId,
            ]);

            return $payableId;
        } catch (MoniteApiException $e) {
            Log::error('Monite API error when creating payable', [
                'external_order_id' => $externalOrder->id,
                'error' => $e->getMessage(),
                'context' => $e->getContext(),
            ]);

            return null;
        } catch (Throwable $e) {
            Log::error('Error when creating Monite payable', [
                'external_order_id' => $externalOrder->id,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Get the invoice file content from storage
     */
    private function getInvoiceFileContent(ExternalOrder $externalOrder): ?string
    {
        if (! Storage::disk(InvoiceSynchronizer::INVOICE_STORAGE_DISK)->exists($externalOrder->invoice_file_path)) {
            return null;
        }

        return Storage::disk(InvoiceSynchronizer::INVOICE_STORAGE_DISK)->get($externalOrder->invoice_file_path);
    }

    /**
     * Upload invoice file to Monite and create payable
     */
    private function uploadFileToMonite(string $filePath, Clinic $clinic): array
    {
        // Client should be configured with the clinic's Monite entity ID
        $client = $this->apiClient->withEntityId($clinic->monite_entity_id);

        // Create a multipart request with the file
        $response = $client->uploadFile('/payables/upload_from_file', $filePath);

        return $response->json();
    }
}
