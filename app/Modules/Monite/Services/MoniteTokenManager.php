<?php

declare(strict_types=1);

namespace App\Modules\Monite\Services;

use App\Modules\Monite\Enums\MoniteGrantType;
use App\Modules\Monite\Exceptions\MoniteApiException;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

final class MoniteTokenManager
{
    private const CACHE_KEY = 'monite_access_token';

    private const CACHE_TTL = 1800; // 30 minutes

    private const REFRESH_THRESHOLD = 300; // 5 minutes before expiry

    public function __construct(
        private readonly string $clientId,
        private readonly string $clientSecret,
        private readonly string $apiBaseUrl,
        private readonly string $apiVersion
    ) {}

    public function getAccessToken(MoniteGrantType $grantType = MoniteGrantType::ClientCredentials, ?string $entityUserId = null): string
    {
        $cacheKey = $this->getCacheKey($grantType, $entityUserId);

        // Check if we have a valid cached token
        $cachedToken = Cache::get($cacheKey);
        if ($cachedToken && $this->isTokenValid($cachedToken)) {
            return $cachedToken['access_token'];
        }

        // Get a new token
        $token = $this->requestNewToken($grantType, $entityUserId);

        // Cache the token
        $this->cacheToken($cacheKey, $token);

        return $token['access_token'];
    }

    public function revokeToken(string $token): bool
    {
        try {
            $response = Http::withHeaders([
                'X-Monite-Version' => $this->apiVersion,
                'Content-Type' => 'application/json',
            ])->post("{$this->apiBaseUrl}/v1/auth/revoke", [
                'client_id' => $this->clientId,
                'client_secret' => $this->clientSecret,
                'token' => $token,
            ]);

            if ($response->successful()) {
                // Clear cached tokens
                $this->clearCachedTokens();

                return true;
            }

            Log::warning('Failed to revoke Monite token', [
                'status' => $response->status(),
                'response' => $response->json(),
            ]);

            return false;
        } catch (Exception $e) {
            Log::error('Exception while revoking Monite token', [
                'exception' => $e->getMessage(),
            ]);

            return false;
        }
    }

    public function getEntityUserToken(string $entityUserId): string
    {
        return $this->getAccessToken(MoniteGrantType::EntityUser, $entityUserId);
    }

    public function clearCachedTokens(): void
    {
        Cache::forget($this->getCacheKey(MoniteGrantType::ClientCredentials));
        Cache::forget($this->getCacheKey(MoniteGrantType::EntityUser));
    }

    private function requestNewToken(MoniteGrantType $grantType, ?string $entityUserId = null): array
    {
        $payload = [
            'grant_type' => $grantType->value,
            'client_id' => $this->clientId,
            'client_secret' => $this->clientSecret,
        ];

        // Add entity_user_id for entity_user grant type
        if ($grantType === MoniteGrantType::EntityUser) {
            if (! $entityUserId) {
                throw MoniteApiException::authenticationFailed('Entity user grant type requires entity_user_id parameter.');
            }
            $payload['entity_user_id'] = $entityUserId;
        }

        $response = Http::withHeaders([
            'X-Monite-Version' => $this->apiVersion,
            'Content-Type' => 'application/json',
        ])->timeout(config('monite.timeout', 30))
            ->post("{$this->apiBaseUrl}/v1/auth/token", $payload);

        if (! $response->successful()) {
            throw MoniteApiException::fromResponse($response);
        }

        $tokenData = $response->json();

        if (! isset($tokenData['access_token'])) {
            throw MoniteApiException::authenticationFailed('Invalid token response from Monite API');
        }

        return $tokenData;
    }

    private function cacheToken(string $cacheKey, array $tokenData): void
    {
        $ttl = $tokenData['expires_in'] ?? self::CACHE_TTL;

        // Cache for slightly less than the actual expiry to ensure we refresh before it expires
        $cacheTtl = max(1, $ttl - self::REFRESH_THRESHOLD);

        Cache::put($cacheKey, $tokenData, $cacheTtl);
    }

    private function isTokenValid(array $tokenData): bool
    {
        if (! isset($tokenData['access_token'])) {
            return false;
        }

        // Check if token is close to expiry
        $expiresIn = $tokenData['expires_in'] ?? 0;

        return $expiresIn > self::REFRESH_THRESHOLD;
    }

    private function getCacheKey(MoniteGrantType $grantType, ?string $entityUserId = null): string
    {
        $key = self::CACHE_KEY.'_'.$grantType->value;

        if ($entityUserId) {
            $key .= '_'.$entityUserId;
        }

        return $key;
    }
}
