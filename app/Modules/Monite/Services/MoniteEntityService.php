<?php

declare(strict_types=1);

namespace App\Modules\Monite\Services;

use App\Models\Clinic;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Exceptions\MoniteApiException;
use App\Modules\Monite\Traits\ChecksMoniteFeatureFlag;
use Illuminate\Support\Facades\Log;

class MoniteEntityService
{
    use ChecksMoniteFeatureFlag;

    public function __construct(
        private readonly MoniteApiClientInterface $moniteClient
    ) {}

    /**
     * Create a Monite entity for a clinic and store the reference
     */
    public function createEntityForClinic(Clinic $clinic): ?string
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'createEntityForClinic')) {
            return null;
        }

        if ($clinic->monite_entity_id) {
            throw new MoniteApiException("Clinic already has a Monite entity: {$clinic->monite_entity_id}");
        }

        try {
            $entityData = $this->prepareEntityData($clinic);

            Log::info('Creating Monite entity - Request payload', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'payload' => $entityData,
            ]);

            $response = $this->moniteClient->post('/entities', $entityData);

            if (! $response->successful()) {
                Log::error('Monite entity creation failed - API Response', [
                    'clinic_id' => $clinic->id,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'response_json' => $response->json(),
                ]);

                throw MoniteApiException::fromResponse($response);
            }

            $responseData = $response->json();
            $entityId = $responseData['id'];

            // Store the entity ID in the clinic
            $clinic->update(['monite_entity_id' => $entityId]);

            Log::info('Monite entity created for clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'monite_entity_id' => $entityId,
            ]);

            return $entityId;

        } catch (MoniteApiException $e) {
            Log::error('Failed to create Monite entity for clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Update a Monite entity when clinic details change
     */
    public function updateEntityForClinic(Clinic $clinic): void
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'updateEntityForClinic')) {
            return;
        }

        if (! $clinic->monite_entity_id) {
            Log::warning('Attempted to update Monite entity for clinic without entity ID', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
            ]);

            return;
        }

        try {
            $entityData = $this->prepareUpdateEntityData($clinic);

            Log::info('Updating Monite entity - Request payload', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'monite_entity_id' => $clinic->monite_entity_id,
                'payload' => $entityData,
            ]);

            $response = $this->moniteClient
                ->withEntityId($clinic->monite_entity_id)
                ->patch("/entities/{$clinic->monite_entity_id}", $entityData);

            if (! $response->successful()) {
                Log::error('Monite entity update failed - API Response', [
                    'clinic_id' => $clinic->id,
                    'monite_entity_id' => $clinic->monite_entity_id,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'response_json' => $response->json(),
                ]);

                throw MoniteApiException::fromResponse($response);
            }

            Log::info('Monite entity updated for clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'monite_entity_id' => $clinic->monite_entity_id,
            ]);

        } catch (MoniteApiException $e) {
            Log::error('Failed to update Monite entity for clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'monite_entity_id' => $clinic->monite_entity_id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Get or create Monite entity for a clinic
     */
    public function getOrCreateEntityForClinic(Clinic $clinic): ?string
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'getOrCreateEntityForClinic')) {
            return null;
        }

        if ($clinic->monite_entity_id) {
            return $clinic->monite_entity_id;
        }

        return $this->createEntityForClinic($clinic);
    }

    /**
     * Delete a Monite entity for a clinic
     */
    public function deleteEntityForClinic(Clinic $clinic): void
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'deleteEntityForClinic')) {
            return;
        }

        if (! $clinic->monite_entity_id) {
            return;
        }

        try {
            $response = $this->moniteClient->delete("/entities/{$clinic->monite_entity_id}");

            if (! $response->successful() && $response->status() !== 404) {
                throw MoniteApiException::fromResponse($response);
            }

            // Clear the entity ID from the clinic
            $clinic->update(['monite_entity_id' => null]);

            Log::info('Monite entity deleted for clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'monite_entity_id' => $clinic->monite_entity_id,
            ]);

        } catch (MoniteApiException $e) {
            Log::error('Failed to delete Monite entity for clinic', [
                'clinic_id' => $clinic->id,
                'clinic_name' => $clinic->name,
                'monite_entity_id' => $clinic->monite_entity_id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Prepare entity data for Monite API entity updates based on clinic information
     */
    private function prepareUpdateEntityData(Clinic $clinic): array
    {
        $entityData = [
            'email' => $this->getClinicEmail($clinic),
            'organization' => [
                'legal_name' => $clinic->name,
            ],
        ];

        // Add tax ID if available
        if ($clinic->business_tax_id) {
            $entityData['tax_id'] = $clinic->business_tax_id;
        }

        // Add phone number if available
        if ($clinic->phone_number) {
            $entityData['phone'] = $clinic->phone_number;
        }

        // Add address - required field for Monite API
        if ($clinic->billingAddress) {
            $address = $clinic->billingAddress;
            $entityData['address'] = [
                'country' => $address->country ?? 'US',
                'city' => $address->city,
                'postal_code' => $address->postal_code,
                'line1' => $address->street,
            ];

            if ($address->state) {
                $entityData['address']['state'] = $address->state;
            }
        } else {
            // Fallback address when clinic doesn't have billing address
            $entityData['address'] = [
                'country' => 'US',
                'city' => 'New York',
                'postal_code' => '10001',
                'state' => 'NY', // Required for US addresses
                'line1' => 'TBD', // To Be Determined - placeholder
            ];
        }

        return $entityData;
    }

    /**
     * Prepare entity data for Monite API entity creation based on clinic information
     */
    private function prepareEntityData(Clinic $clinic): array
    {
        $entityData = [
            'type' => 'organization',
            'email' => $this->getClinicEmail($clinic),
            'organization' => [
                'legal_name' => $clinic->name,
            ],
        ];

        // Add tax ID if available
        if ($clinic->business_tax_id) {
            $entityData['tax_id'] = $clinic->business_tax_id;
        }

        // Add phone number if available
        if ($clinic->phone_number) {
            $entityData['phone'] = $clinic->phone_number;
        }

        // Add address - required field for Monite API
        if ($clinic->billingAddress) {
            $address = $clinic->billingAddress;
            $entityData['address'] = [
                'country' => $address->country ?? 'US',
                'city' => $address->city,
                'postal_code' => $address->postal_code,
                'line1' => $address->street,
            ];

            if ($address->state) {
                $entityData['address']['state'] = $address->state;
            }
        } else {
            // Fallback address when clinic doesn't have billing address
            $entityData['address'] = [
                'country' => 'US',
                'city' => 'New York',
                'postal_code' => '10001',
                'state' => 'NY', // Required for US addresses
                'line1' => 'TBD', // To Be Determined - placeholder
            ];
        }

        return $entityData;
    }

    /**
     * Get email for clinic - we'll need to determine the best way to get this
     * For now, we'll use a placeholder approach
     */
    private function getClinicEmail(Clinic $clinic): string
    {
        // Try to get email from clinic managers first
        if ($clinic->managers?->first()?->email) {
            return $clinic->managers->first()->email;
        }

        // Try to get email from any clinic user
        if ($clinic->users?->first()?->email) {
            return $clinic->users->first()->email;
        }

        // Fallback to clinic ID-based email
        return "{$clinic->id}@highfive.vet";
    }
}
