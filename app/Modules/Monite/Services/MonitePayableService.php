<?php

declare(strict_types=1);

namespace App\Modules\Monite\Services;

use App\Models\Clinic;
use App\Modules\Monite\Jobs\CreatePayableFromInvoiceJob;
use App\Modules\Monite\Traits\ChecksMoniteFeatureFlag;
use App\Modules\Order\Models\ExternalOrder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

final class MonitePayableService
{
    use ChecksMoniteFeatureFlag;

    /**
     * Create a new service instance.
     */
    public function __construct(
        private readonly MoniteFeatureFlagService $featureFlagService
    ) {}

    /**
     * Process all invoices for a clinic and create payables in Monite.
     */
    public function processClinicInvoices(Clinic $clinic): int
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'processClinicInvoices')) {
            return 0;
        }

        $externalOrders = $this->getClinicInvoicesToProcess($clinic);

        Log::info('Processing clinic invoices for Monite payables', [
            'clinic_id' => $clinic->id,
            'invoice_count' => $externalOrders->count(),
        ]);

        foreach ($externalOrders as $externalOrder) {
            CreatePayableFromInvoiceJob::dispatch($externalOrder, $clinic);
        }

        return $externalOrders->count();
    }

    /**
     * Process a specific invoice for a clinic and create a payable in Monite.
     */
    public function processInvoice(ExternalOrder $externalOrder): void
    {
        $clinic = $externalOrder->subOrder->order->clinic;

        if (! $this->ensureMoniteFeatureEnabled($clinic, 'processInvoice')) {
            return;
        }

        CreatePayableFromInvoiceJob::dispatch($externalOrder, $clinic);
    }

    /**
     * Get all external orders with invoices that haven't been processed yet.
     */
    private function getClinicInvoicesToProcess(Clinic $clinic): Collection
    {
        return ExternalOrder::query()
            ->whereNotNull('invoice_file_path')
            ->whereNull('monite_payable_id')
            ->whereHas('subOrder.order', function ($query) use ($clinic) {
                $query->where('clinic_id', $clinic->id);
            })
            ->get();
    }
}
