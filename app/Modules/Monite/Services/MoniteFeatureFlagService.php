<?php

declare(strict_types=1);

namespace App\Modules\Monite\Services;

use App\Models\Clinic;
use Illuminate\Support\Facades\Log;
use Laravel\Pennant\Feature;

final class MoniteFeatureFlagService
{
    private const FEATURE_NAME = 'monite-integration';

    /**
     * Check if Monite integration is enabled for the given clinic
     */
    public function isEnabled(Clinic $clinic): bool
    {
        return Feature::for($clinic)->active(self::FEATURE_NAME);
    }

    /**
     * Enable Monite integration for the given clinic
     */
    public function enable(Clinic $clinic): void
    {
        Feature::for($clinic)->activate(self::FEATURE_NAME);

        Log::info('Monite feature enabled for clinic', [
            'clinic_id' => $clinic->id,
            'clinic_name' => $clinic->name,
        ]);
    }

    /**
     * Disable Monite integration for the given clinic
     */
    public function disable(Clinic $clinic): void
    {
        Feature::for($clinic)->deactivate(self::FEATURE_NAME);

        Log::info('Monite feature disabled for clinic', [
            'clinic_id' => $clinic->id,
            'clinic_name' => $clinic->name,
        ]);
    }

    /**
     * Toggle Monite integration for the given clinic
     */
    public function toggle(Clinic $clinic): bool
    {
        $isCurrentlyEnabled = $this->isEnabled($clinic);

        if ($isCurrentlyEnabled) {
            $this->disable($clinic);

            return false;
        }

        $this->enable($clinic);

        return true;
    }

    /**
     * Check if Monite integration is enabled and handle gracefully if not
     * Returns true if enabled, false if disabled (logs the skip)
     */
    public function ensureEnabled(Clinic $clinic, string $operation): bool
    {
        if ($this->isEnabled($clinic)) {
            return true;
        }

        $this->logSkippedOperation($clinic, $operation);

        return false;
    }

    /**
     * Log when a Monite operation is skipped due to feature being disabled
     */
    private function logSkippedOperation(Clinic $clinic, string $operation): void
    {
        Log::info('Monite operation skipped - feature disabled for clinic', [
            'clinic_id' => $clinic->id,
            'clinic_name' => $clinic->name,
            'operation' => $operation,
        ]);
    }
}
