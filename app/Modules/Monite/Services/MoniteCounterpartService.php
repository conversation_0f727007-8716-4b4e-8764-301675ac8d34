<?php

declare(strict_types=1);

namespace App\Modules\Monite\Services;

use App\Models\Clinic;
use App\Models\Vendor;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Exceptions\MoniteApiException;
use App\Modules\Monite\Models\MoniteVendorMapping;
use App\Modules\Monite\Traits\ChecksMoniteFeatureFlag;
use Illuminate\Support\Facades\Log;
use Throwable;

final class MoniteCounterpartService
{
    use ChecksMoniteFeatureFlag;

    public function __construct(
        private readonly MoniteApiClientInterface $apiClient
    ) {}

    public function syncAllVendorsForClinic(Clinic $clinic): int
    {
        $this->ensureMoniteFeatureEnabled($clinic, 'sync_counterparts');

        $vendors = $this->getVendorsToSync($clinic);
        $syncedCount = 0;

        Log::info('Syncing vendors to Monite counterparts', [
            'clinic_id' => $clinic->id,
            'vendor_count' => $vendors->count(),
        ]);

        foreach ($vendors as $vendor) {
            if ($this->syncVendorCounterpart($vendor, $clinic)) {
                $syncedCount++;
            }
        }

        return $syncedCount;
    }

    public function syncVendorCounterpart(Vendor $vendor, Clinic $clinic): bool
    {
        $this->ensureMoniteFeatureEnabled($clinic, 'sync_counterparts');

        try {
            // Check if mapping exists
            $mapping = MoniteVendorMapping::where('vendor_id', $vendor->id)
                ->where('clinic_id', $clinic->id)
                ->first();

            if ($mapping) {
                // Update existing counterpart
                return $this->updateCounterpart($vendor, $clinic, $mapping);
            }

            // Create new counterpart
            return $this->createCounterpart($vendor, $clinic);

        } catch (Throwable $e) {
            Log::error('Error occurred while syncing vendor with Monite', [
                'vendor_id' => $vendor->id,
                'clinic_id' => $clinic->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    private function getVendorsToSync(Clinic $clinic)
    {
        return Vendor::isEnabled()->get();
    }

    private function updateCounterpart(Vendor $vendor, Clinic $clinic, MoniteVendorMapping $mapping): bool
    {
        $client = $this->apiClient->withEntityId($clinic->monite_entity_id);
        $payload = $this->buildUpdatePayload($vendor);

        try {
            $response = $client->patch("/counterparts/{$mapping->monite_counterpart_id}", $payload);

            Log::info('Updated counterpart successfully', [
                'vendor_id' => $vendor->id,
                'clinic_id' => $clinic->id,
                'monite_counterpart_id' => $mapping->monite_counterpart_id,
            ]);

            return true;

        } catch (MoniteApiException $e) {
            Log::error('Failed to update counterpart', [
                'vendor_id' => $vendor->id,
                'clinic_id' => $clinic->id,
                'monite_counterpart_id' => $mapping->monite_counterpart_id,
                'error_code' => $e->getCode(),
                'error_message' => $e->getMessage(),
                'payload' => $payload,
                'response' => $e->errorDetails ?? 'No response details',
            ]);

            return false;
        }
    }

    private function createCounterpart(Vendor $vendor, Clinic $clinic): bool
    {
        $client = $this->apiClient->withEntityId($clinic->monite_entity_id);
        $payload = $this->buildCreatePayload($vendor);

        try {
            $response = $client->post('/counterparts', $payload);
            $data = $response->json();

            if (isset($data['id']) && is_string($data['id']) && mb_strlen($data['id']) === 36) {
                // Create mapping
                MoniteVendorMapping::create([
                    'vendor_id' => $vendor->id,
                    'clinic_id' => $clinic->id,
                    'monite_counterpart_id' => $data['id'],
                ]);

                Log::info('Created counterpart successfully', [
                    'vendor_id' => $vendor->id,
                    'clinic_id' => $clinic->id,
                    'monite_counterpart_id' => $data['id'],
                ]);

                return true;
            }

            Log::error('Invalid response from Monite API', [
                'vendor_id' => $vendor->id,
                'clinic_id' => $clinic->id,
                'response_data' => $data,
            ]);

            return false;

        } catch (MoniteApiException $e) {
            Log::error('Failed to create counterpart', [
                'vendor_id' => $vendor->id,
                'clinic_id' => $clinic->id,
                'error_code' => $e->getCode(),
                'error_message' => $e->getMessage(),
                'payload' => $payload,
                'response' => $e->errorDetails ?? 'No response details',
            ]);

            return false;
        }
    }

    private function buildCreatePayload(Vendor $vendor): array
    {
        return [
            'type' => 'organization',
            'organization' => [
                'legal_name' => $vendor->name,
                'is_customer' => true,
                'is_vendor' => true,
                'address' => [
                    'city' => 'Berlin',
                    'country' => 'AF',
                    'line1' => 'Flughafenstrasse 52',
                    'postal_code' => '10115',
                ],
                'registered_address' => [
                    'city' => 'Berlin',
                    'country' => 'AF',
                    'line1' => 'Flughafenstrasse 52',
                    'postal_code' => '10115',
                ],
            ],
        ];
    }

    private function buildUpdatePayload(Vendor $vendor): array
    {
        return [
            'organization' => [
                'legal_name' => $vendor->name,
                'is_customer' => false,
                'is_vendor' => true,
            ],
        ];
    }
}
