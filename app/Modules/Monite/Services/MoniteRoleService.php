<?php

declare(strict_types=1);

namespace App\Modules\Monite\Services;

use App\Models\Clinic;
use App\Modules\Account\Models\Role;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Exceptions\MoniteApiException;
use App\Modules\Monite\Models\MoniteRoleMapping;
use App\Modules\Monite\Traits\ChecksMoniteFeatureFlag;
use Illuminate\Support\Facades\Log;

class MoniteRoleService
{
    use ChecksMoniteFeatureFlag;

    public function __construct(
        private readonly MoniteApiClientInterface $moniteClient
    ) {}

    /**
     * Create or update a Monite role for a clinic role
     */
    public function createOrUpdateRoleForClinic(Role $role, Clinic $clinic): ?string
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'createOrUpdateRoleForClinic')) {
            return null;
        }

        $mapping = MoniteRoleMapping::where('role_id', $role->id)
            ->where('clinic_id', $clinic->id)
            ->first();

        if ($mapping) {
            return $this->updateMoniteRole($mapping, $role, $clinic);
        }

        return $this->createMoniteRole($role, $clinic);
    }

    /**
     * Delete a Monite role mapping
     */
    public function deleteRoleForClinic(Role $role, Clinic $clinic): void
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'deleteRoleForClinic')) {
            return;
        }

        $mapping = MoniteRoleMapping::where('role_id', $role->id)
            ->where('clinic_id', $clinic->id)
            ->first();

        if (! $mapping) {
            return;
        }

        try {
            $response = $this->moniteClient
                ->withEntityId($mapping->entity_id)
                ->delete("/roles/{$mapping->monite_role_id}");

            if (! $response->successful() && $response->status() !== 404) {
                Log::error('Monite role deletion failed - API Response', [
                    'clinic_id' => $clinic->id,
                    'role_id' => $role->id,
                    'monite_role_id' => $mapping->monite_role_id,
                    'entity_id' => $mapping->entity_id,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'response_json' => $response->json(),
                ]);

                throw MoniteApiException::fromResponse($response);
            }

            // Delete the mapping
            $mapping->delete();

            Log::info('Monite role deleted for clinic', [
                'clinic_id' => $clinic->id,
                'role_id' => $role->id,
                'role_name' => $role->name,
                'monite_role_id' => $mapping->monite_role_id,
                'entity_id' => $mapping->entity_id,
            ]);

        } catch (MoniteApiException $e) {
            Log::error('Failed to delete Monite role for clinic', [
                'clinic_id' => $clinic->id,
                'role_id' => $role->id,
                'role_name' => $role->name,
                'monite_role_id' => $mapping->monite_role_id,
                'entity_id' => $mapping->entity_id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Get Monite role ID for a clinic role
     */
    public function getMoniteRoleId(Role $role, Clinic $clinic): ?string
    {
        $mapping = MoniteRoleMapping::where('role_id', $role->id)
            ->where('clinic_id', $clinic->id)
            ->first();

        return $mapping?->monite_role_id;
    }

    /**
     * Sync all clinic roles with Monite
     */
    public function syncAllRolesForClinic(Clinic $clinic): void
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'syncAllRolesForClinic')) {
            return;
        }

        if (! $clinic->monite_entity_id) {
            throw new MoniteApiException('Clinic must have a Monite entity before syncing roles');
        }

        $clinicRoles = Role::whereIn('name', ['clinic:owner', 'clinic:admin', 'clinic:manager'])->get();

        foreach ($clinicRoles as $role) {
            $this->createOrUpdateRoleForClinic($role, $clinic);
        }
    }

    /**
     * Delete all Monite roles for a clinic
     */
    public function deleteAllRolesForClinic(Clinic $clinic): void
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'deleteAllRolesForClinic')) {
            return;
        }

        $mappings = MoniteRoleMapping::where('clinic_id', $clinic->id)->get();

        foreach ($mappings as $mapping) {
            try {
                $response = $this->moniteClient
                    ->withEntityId($mapping->entity_id)
                    ->delete("/roles/{$mapping->monite_role_id}");

                if (! $response->successful() && $response->status() !== 404) {
                    Log::error('Monite role deletion failed - API Response', [
                        'clinic_id' => $clinic->id,
                        'role_id' => $mapping->role_id,
                        'monite_role_id' => $mapping->monite_role_id,
                        'entity_id' => $mapping->entity_id,
                        'status_code' => $response->status(),
                        'response_body' => $response->body(),
                    ]);
                }

                $mapping->delete();

            } catch (MoniteApiException $e) {
                Log::error('Failed to delete Monite role for clinic', [
                    'clinic_id' => $clinic->id,
                    'role_id' => $mapping->role_id,
                    'monite_role_id' => $mapping->monite_role_id,
                    'entity_id' => $mapping->entity_id,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * Create a new Monite role
     */
    private function createMoniteRole(Role $role, Clinic $clinic): string
    {
        if (! $clinic->monite_entity_id) {
            throw new MoniteApiException('Clinic must have a Monite entity before creating roles');
        }

        try {
            $roleData = $this->prepareRoleData($role);

            Log::info('Creating Monite role - Request payload', [
                'clinic_id' => $clinic->id,
                'role_id' => $role->id,
                'role_name' => $role->name,
                'entity_id' => $clinic->monite_entity_id,
                'payload' => $roleData,
            ]);

            $response = $this->moniteClient
                ->withEntityId($clinic->monite_entity_id)
                ->post('/roles', $roleData);

            if (! $response->successful()) {
                Log::error('Monite role creation failed - API Response', [
                    'clinic_id' => $clinic->id,
                    'role_id' => $role->id,
                    'entity_id' => $clinic->monite_entity_id,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'response_json' => $response->json(),
                ]);

                throw MoniteApiException::fromResponse($response);
            }

            $responseData = $response->json();
            $moniteRoleId = $responseData['id'];

            // Create the mapping
            MoniteRoleMapping::create([
                'role_id' => $role->id,
                'monite_role_id' => $moniteRoleId,
                'clinic_id' => $clinic->id,
                'entity_id' => $clinic->monite_entity_id,
            ]);

            Log::info('Monite role created for clinic', [
                'clinic_id' => $clinic->id,
                'role_id' => $role->id,
                'role_name' => $role->name,
                'monite_role_id' => $moniteRoleId,
                'entity_id' => $clinic->monite_entity_id,
            ]);

            return $moniteRoleId;

        } catch (MoniteApiException $e) {
            Log::error('Failed to create Monite role for clinic', [
                'clinic_id' => $clinic->id,
                'role_id' => $role->id,
                'role_name' => $role->name,
                'entity_id' => $clinic->monite_entity_id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Update an existing Monite role
     */
    private function updateMoniteRole(MoniteRoleMapping $mapping, Role $role, Clinic $clinic): string
    {
        try {
            $roleData = $this->prepareRoleData($role);

            Log::info('Updating Monite role - Request payload', [
                'clinic_id' => $clinic->id,
                'role_id' => $role->id,
                'role_name' => $role->name,
                'monite_role_id' => $mapping->monite_role_id,
                'entity_id' => $mapping->entity_id,
                'payload' => $roleData,
            ]);

            $response = $this->moniteClient
                ->withEntityId($mapping->entity_id)
                ->patch("/roles/{$mapping->monite_role_id}", $roleData);

            if (! $response->successful()) {
                Log::error('Monite role update failed - API Response', [
                    'clinic_id' => $clinic->id,
                    'role_id' => $role->id,
                    'monite_role_id' => $mapping->monite_role_id,
                    'entity_id' => $mapping->entity_id,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'response_json' => $response->json(),
                ]);

                throw MoniteApiException::fromResponse($response);
            }

            Log::info('Monite role updated for clinic', [
                'clinic_id' => $clinic->id,
                'role_id' => $role->id,
                'role_name' => $role->name,
                'monite_role_id' => $mapping->monite_role_id,
                'entity_id' => $mapping->entity_id,
            ]);

            return $mapping->monite_role_id;

        } catch (MoniteApiException $e) {
            Log::error('Failed to update Monite role for clinic', [
                'clinic_id' => $clinic->id,
                'role_id' => $role->id,
                'role_name' => $role->name,
                'monite_role_id' => $mapping->monite_role_id,
                'entity_id' => $mapping->entity_id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Prepare role data for Monite API based on local role
     */
    private function prepareRoleData(Role $role): array
    {
        $permissions = $this->getPermissionsForRole($role);

        return [
            'name' => $this->generateMoniteRoleName($role),
            'permissions' => $permissions,
        ];
    }

    /**
     * Generate a unique role name for Monite
     */
    private function generateMoniteRoleName(Role $role): string
    {
        // Keep the original role name as is
        return $role->name;
    }

    /**
     * Get permissions configuration for a role based on clinic role type
     */
    private function getPermissionsForRole(Role $role): array
    {
        $rolePermissions = config("monite.roles.{$role->name}");

        if ($rolePermissions) {
            return $rolePermissions;
        }

        return config('monite.default_permissions');
    }
}
