<?php

declare(strict_types=1);

namespace App\Modules\Monite\Models;

use App\Models\Clinic;
use App\Modules\Account\Models\Role;
use Database\Factories\Modules\Monite\Models\MoniteRoleMappingFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class MoniteRoleMapping extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'role_id',
        'monite_role_id',
        'clinic_id',
        'entity_id',
    ];

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    protected static function newFactory(): MoniteRoleMappingFactory
    {
        return MoniteRoleMappingFactory::new();
    }
}
