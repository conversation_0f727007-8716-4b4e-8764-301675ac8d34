<?php

declare(strict_types=1);

namespace App\Modules\Monite\Console\Commands;

use App\Models\Clinic;
use App\Modules\Monite\Jobs\SyncMoniteUsersJob;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use Illuminate\Console\Command;

final class SyncMoniteUsers extends Command
{
    protected $signature = 'monite:sync-users {--clinic-id= : Sync users for a specific clinic} {--queue : Use queue for job processing}';

    protected $description = 'Sync Monite users for all clinics or a specific clinic';

    public function __construct(
        private readonly MoniteFeatureFlagService $moniteFeatureService
    ) {
        parent::__construct();
    }

    public function handle(): int
    {
        $clinicId = $this->option('clinic-id');
        $useQueue = $this->option('queue');

        if ($clinicId) {
            $clinic = Clinic::find($clinicId);

            if (! $clinic) {
                $this->error("Clinic with ID {$clinicId} not found.");

                return 1;
            }

            // Check if Monite feature is enabled for this clinic
            if (! $this->moniteFeatureService->isEnabled($clinic)) {
                $this->warn("Monite feature is disabled for clinic {$clinic->name} (ID: {$clinic->id}). Skipping.");

                return 0; // Success exit, just skipped
            }

            if (! $clinic->monite_entity_id) {
                $this->error("Clinic {$clinic->name} does not have a Monite entity ID.");

                return 1;
            }

            $this->syncUsersForClinic($clinic, $useQueue);
            $this->info("Monite users synced for clinic: {$clinic->name}");
        } else {
            $clinics = Clinic::whereNotNull('monite_entity_id')->get();

            if ($clinics->isEmpty()) {
                $this->info('No clinics with Monite entity IDs found.');

                return 0;
            }

            $this->info("Found {$clinics->count()} clinic(s) with Monite entity IDs.");

            foreach ($clinics as $clinic) {
                $this->syncUsersForClinic($clinic, $useQueue);
            }

            $this->info('Monite users synced for all clinics.');
        }

        return 0;
    }

    private function syncUsersForClinic(Clinic $clinic, bool $useQueue): void
    {
        if (! $this->moniteFeatureService->isEnabled($clinic)) {
            $this->warn("Monite feature is disabled for clinic {$clinic->name} (ID: {$clinic->id}). Skipping.");

            return;
        }

        if ($useQueue) {
            SyncMoniteUsersJob::dispatch($clinic);
            $this->line("User sync job queued for clinic: {$clinic->name}");
        } else {
            $job = new SyncMoniteUsersJob($clinic);
            $action = app(\App\Modules\Monite\Actions\SyncUsersAction::class);
            $job->handle($action);
            $this->line("Users synced synchronously for clinic: {$clinic->name}");
        }
    }
}
