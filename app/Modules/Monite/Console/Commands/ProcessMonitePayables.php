<?php

declare(strict_types=1);

namespace App\Modules\Monite\Console\Commands;

use App\Models\Clinic;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use App\Modules\Monite\Services\MonitePayableService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Throwable;

final class ProcessMonitePayables extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'monite:process-payables {--clinic-id= : Process invoices for a specific clinic}';

    /**
     * The console command description.
     */
    protected $description = 'Process invoices and create payables in Monite';

    public function __construct(
        private readonly MonitePayableService $payableService,
        private readonly MoniteFeatureFlagService $featureFlagService
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting Monite payables processing...');
        $clinicId = $this->option('clinic-id');

        // Process for a specific clinic if ID is provided
        if ($clinicId) {
            $clinic = Clinic::find($clinicId);

            if (! $clinic) {
                $this->error("Clinic with ID {$clinicId} not found");

                return 1;
            }

            $this->processClinic($clinic);

            return 0;
        }

        // Process for all clinics with Monite enabled
        $clinics = $this->getClinicsWithMonite();

        if ($clinics->isEmpty()) {
            $this->info('No clinics with Monite enabled found');

            return 0;
        }

        $totalProcessed = 0;
        foreach ($clinics as $clinic) {
            $processed = $this->processClinic($clinic);
            $totalProcessed += $processed;
        }

        $this->info("Processing complete. Total invoices queued for processing: {$totalProcessed}");

        return 0;
    }

    /**
     * Process invoices for a specific clinic
     */
    private function processClinic(Clinic $clinic): int
    {
        $this->info("Processing invoices for clinic: {$clinic->name} (ID: {$clinic->id})");

        try {
            $processed = $this->payableService->processClinicInvoices($clinic);

            $this->info("Queued {$processed} invoice(s) for processing");

            return $processed;
        } catch (Throwable $e) {
            $this->error("Error processing invoices for clinic {$clinic->id}: {$e->getMessage()}");
            Log::error('Error in Monite payable processing', [
                'clinic_id' => $clinic->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return 0;
        }
    }

    /**
     * Get all clinics that have Monite enabled
     */
    private function getClinicsWithMonite(): Collection
    {
        return Clinic::query()
            ->whereNotNull('monite_entity_id')
            ->get();
    }
}
