<?php

declare(strict_types=1);

namespace App\Modules\Monite\Console\Commands;

use App\Models\Clinic;
use App\Modules\Monite\Jobs\SyncAllVendorCounterpartsJob;
use App\Modules\Monite\Services\MoniteCounterpartService;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use Illuminate\Console\Command;

final class SyncMoniteCounterparts extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'monite:sync-counterparts 
                            {--clinic-id= : Process vendors for a specific clinic}
                            {--queue : Process vendors in the background}';

    /**
     * The console command description.
     */
    protected $description = 'Sync vendors to Monite counterparts';

    public function __construct(
        private readonly MoniteCounterpartService $counterpartService,
        private readonly MoniteFeatureFlagService $featureFlagService
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting Monite counterparts sync...');

        $clinicId = $this->option('clinic-id');
        $useQueue = $this->option('queue');

        // Process all vendors for a specific clinic
        if ($clinicId) {
            return $this->processClinic($clinicId, $useQueue);
        }

        // Process all vendors for all enabled clinics
        return $this->processAll($useQueue);
    }

    /**
     * Process all vendors for a specific clinic
     */
    private function processClinic(string $clinicId, bool $useQueue): int
    {
        $clinic = Clinic::find($clinicId);
        if (! $clinic) {
            $this->error("Clinic with ID {$clinicId} not found");

            return 1;
        }

        if ($this->featureFlagService->isEnabled($clinic)) {
            $this->info("Processing all vendors for clinic {$clinic->name}");

            if ($useQueue) {
                SyncAllVendorCounterpartsJob::dispatch($clinic);
                $this->info('Vendor sync job dispatched to queue');

                return 0;
            }

            $count = $this->counterpartService->syncAllVendorsForClinic($clinic);
            $this->info("{$count} vendors synced successfully with Monite counterparts");

            return 0;
        }

        $this->warn("Monite is not enabled for clinic {$clinic->name}");

        return 0;

    }

    /**
     * Process all vendors for all enabled clinics
     */
    private function processAll(bool $useQueue): int
    {
        $clinics = Clinic::whereNotNull('monite_entity_id')->get();
        if ($clinics->isEmpty()) {
            $this->info('No clinics with Monite enabled found');

            return 0;
        }

        $totalCount = 0;
        $jobCount = 0;
        foreach ($clinics as $clinic) {
            if ($this->featureFlagService->isEnabled($clinic)) {
                if ($useQueue) {
                    SyncAllVendorCounterpartsJob::dispatch($clinic);
                    $jobCount++;
                    $this->info("Queued vendor sync job for clinic {$clinic->name}");
                } else {
                    $count = $this->counterpartService->syncAllVendorsForClinic($clinic);
                    $totalCount += $count;
                    $this->info("Synced {$count} vendors for clinic {$clinic->name}");
                }
            } else {
                $this->warn("Monite is not enabled for clinic {$clinic->name}");
            }
        }

        if ($useQueue) {
            $this->info("Total of {$jobCount} jobs dispatched to queue");
        } else {
            $this->info("Total of {$totalCount} vendors synced successfully");
        }

        return 0;
    }
}
