<?php

declare(strict_types=1);

namespace App\Modules\Monite\Console\Commands;

use App\Models\Clinic;
use App\Modules\Monite\Jobs\SyncMoniteRolesJob;
use Illuminate\Console\Command;
use Laravel\Pennant\Feature;

final class SyncMoniteRoles extends Command
{
    protected $signature = 'monite:sync-roles {--clinic-id= : Sync roles for specific clinic} {--queue : Process roles in queue}';

    protected $description = 'Sync user roles with Monite entity roles';

    public function handle(): int
    {
        $clinicId = $this->option('clinic-id');
        $useQueue = $this->option('queue');

        if ($clinicId) {
            $clinic = Clinic::find($clinicId);
            if (! $clinic) {
                $this->error("Clinic with ID {$clinicId} not found.");

                return 1;
            }

            // Check if Monite feature is enabled for this clinic
            if (! Feature::for($clinic)->active('monite-integration')) {
                $this->warn("Monite feature is disabled for clinic {$clinic->name} (ID: {$clinic->id}). Skipping.");

                return 0;
            }

            if (! $clinic->monite_entity_id) {
                $this->error("Clinic {$clinic->name} does not have a Monite entity ID.");

                return 1;
            }

            $this->info("Syncing Monite roles for clinic: {$clinic->name}");
            $this->syncRolesForClinic($clinic, $useQueue);
        } else {
            $clinics = Clinic::whereNotNull('monite_entity_id')->get();

            if ($clinics->isEmpty()) {
                $this->info('No clinics with Monite entity IDs found.');

                return 0;
            }

            $this->info("Found {$clinics->count()} clinics with Monite entities. Starting role sync...");

            $bar = $this->output->createProgressBar($clinics->count());
            $bar->start();

            foreach ($clinics as $clinic) {
                $this->syncRolesForClinic($clinic, $useQueue);
                $bar->advance();
            }

            $bar->finish();
            $this->newLine();
        }

        $this->info('Monite role sync completed successfully!');

        return 0;
    }

    private function syncRolesForClinic(Clinic $clinic, bool $useQueue): void
    {
        // Check if Monite feature is enabled for this clinic
        if (! Feature::for($clinic)->active('monite-integration')) {
            $this->warn("Monite feature is disabled for clinic {$clinic->name} (ID: {$clinic->id}). Skipping.");

            return;
        }

        if (! $clinic->monite_entity_id) {
            $this->warn("Clinic {$clinic->name} (ID: {$clinic->id}) does not have a Monite entity ID. Skipping.");

            return;
        }

        $this->info("Syncing roles for clinic: {$clinic->name} (ID: {$clinic->id})");

        if ($useQueue) {
            SyncMoniteRolesJob::dispatch($clinic);
            $this->line("Role sync job queued for clinic: {$clinic->name}");
        } else {
            $job = new SyncMoniteRolesJob($clinic);
            $action = app(\App\Modules\Monite\Actions\SyncRolesAction::class);
            $job->handle($action);
            $this->line("Roles synced synchronously for clinic: {$clinic->name}");
        }
    }
}
