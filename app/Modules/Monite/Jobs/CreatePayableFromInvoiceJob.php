<?php

declare(strict_types=1);

namespace App\Modules\Monite\Jobs;

use App\Models\Clinic;
use App\Modules\Monite\Actions\CreatePayableFromInvoiceAction;
use App\Modules\Order\Models\ExternalOrder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

final class CreatePayableFromInvoiceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     */
    public int $backoff = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(
        private readonly ExternalOrder $externalOrder,
        private readonly Clinic $clinic
    ) {}

    /**
     * Execute the job.
     */
    public function handle(CreatePayableFromInvoiceAction $action): void
    {
        Log::info('Processing Monite payable creation job', [
            'external_order_id' => $this->externalOrder->id,
            'clinic_id' => $this->clinic->id,
        ]);

        $payableId = $action->execute($this->externalOrder, $this->clinic);

        if ($payableId) {
            Log::info('Successfully created Monite payable', [
                'external_order_id' => $this->externalOrder->id,
                'monite_payable_id' => $payableId,
            ]);
        } else {
            Log::error('Failed to create Monite payable', [
                'external_order_id' => $this->externalOrder->id,
                'job_attempts' => $this->attempts(),
            ]);
        }
    }
}
