<?php

declare(strict_types=1);

namespace App\Modules\Monite\Jobs;

use App\Models\Clinic;
use App\Modules\Monite\Services\MoniteCounterpartService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

final class SyncAllVendorCounterpartsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     */
    public int $backoff = 60;

    /**
     * Create a new job instance.
     */
    public function __construct(
        private readonly Clinic $clinic
    ) {}

    /**
     * Execute the job.
     */
    public function handle(MoniteCounterpartService $counterpartService): void
    {
        Log::info('Starting vendor counterpart sync for clinic', [
            'clinic_id' => $this->clinic->id,
            'clinic_name' => $this->clinic->name,
        ]);

        $syncedCount = $counterpartService->syncAllVendorsForClinic($this->clinic);

        Log::info('Completed vendor counterpart sync for clinic', [
            'clinic_id' => $this->clinic->id,
            'clinic_name' => $this->clinic->name,
            'synced_count' => $syncedCount,
        ]);
    }
}
