<?php

declare(strict_types=1);

namespace App\Modules\Monite\Jobs;

use App\Models\Clinic;
use App\Modules\Monite\Actions\DeleteEntityAction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

final class DeleteMoniteEntityJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        private readonly Clinic $clinic
    ) {}

    public function handle(DeleteEntityAction $action): bool
    {
        return $action->execute($this->clinic);
    }

    public function getClinic(): Clinic
    {
        return $this->clinic;
    }
}
