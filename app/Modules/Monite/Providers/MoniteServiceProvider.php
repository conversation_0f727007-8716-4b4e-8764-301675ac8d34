<?php

declare(strict_types=1);

namespace App\Modules\Monite\Providers;

use App\Modules\Monite\Console\Commands\ProcessMonitePayables;
use App\Modules\Monite\Console\Commands\SyncMoniteCounterparts;
use App\Modules\Monite\Console\Commands\SyncMoniteRoles;
use App\Modules\Monite\Console\Commands\SyncMoniteUsers;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Services\MoniteApiClient;
use App\Modules\Monite\Services\MoniteTokenManager;
use Illuminate\Support\ServiceProvider;

final class MoniteServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->mergeConfigFrom(__DIR__.'/../config/monite.php', 'monite');

        // Register token manager
        $this->app->singleton(MoniteTokenManager::class, function ($app) {
            $config = config('monite');
            $environment = $config['environment'];
            $environments = $config['environments'][$environment];

            $clientId = $config['credentials']['client_id'] ?? '';
            $clientSecret = $config['credentials']['client_secret'] ?? '';

            return new MoniteTokenManager(
                clientId: $clientId,
                clientSecret: $clientSecret,
                apiBaseUrl: $environments['api_base_url'],
                apiVersion: $config['api_version']
            );
        });

        // Register API client
        $this->app->singleton(MoniteApiClient::class, function ($app) {
            $config = config('monite');
            $environment = $config['environment'];
            $environments = $config['environments'][$environment];

            return new MoniteApiClient(
                tokenManager: $app->make(MoniteTokenManager::class),
                apiBaseUrl: $environments['api_base_url'],
                apiVersion: $config['api_version']
            );
        });

        // Bind interface to implementation
        $this->app->bind(MoniteApiClientInterface::class, MoniteApiClient::class);
    }

    public function boot(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                SyncMoniteRoles::class,
                SyncMoniteUsers::class,
                ProcessMonitePayables::class,
                SyncMoniteCounterparts::class,
            ]);
        }
    }
}
