<?php

declare(strict_types=1);

namespace App\Modules\Monite\Traits;

use App\Models\Clinic;
use App\Modules\Monite\Services\MoniteFeatureFlagService;

trait ChecksMoniteFeatureFlag
{
    protected function getMoniteFeatureFlagService(): MoniteFeatureFlagService
    {
        return app(MoniteFeatureFlagService::class);
    }

    /**
     * Check if Monite integration is enabled for the given clinic
     */
    protected function isMoniteEnabledForClinic(Clinic $clinic): bool
    {
        return $this->getMoniteFeatureFlagService()->isEnabled($clinic);
    }

    /**
     * Gracefully handle when Monite feature is disabled for a clinic
     * Logs the skip action but doesn't throw an exception
     */
    protected function handleMoniteFeatureDisabled(Clinic $clinic, string $operation): void
    {
        // This method is no longer needed as the service handles logging
    }

    /**
     * Check if Monite feature is enabled and handle gracefully if not
     * Returns true if enabled, false if disabled (logs the skip)
     */
    protected function ensureMoniteFeatureEnabled(Clinic $clinic, string $operation): bool
    {
        return $this->getMoniteFeatureFlagService()->ensureEnabled($clinic, $operation);
    }
}
