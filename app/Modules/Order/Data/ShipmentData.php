<?php

declare(strict_types=1);

namespace App\Modules\Order\Data;

use Carbon\Carbon;
use Spatie\LaravelData\Data;

final class ShipmentData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $trackingNumber,
        public readonly string $carrier,
        public readonly ?string $trackingLink,
        public readonly ?Carbon $etaDateFrom,
        public readonly ?Carbon $etaDateTo,
        public readonly ?Carbon $dateDelivered,
    ) {}
}
