<?php

declare(strict_types=1);

namespace App\Modules\Order\Actions;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\SubOrder;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
use Rap2hpoutre\FastExcel\FastExcel;

final class ExportOrderCsv
{
    public function handle(string $orderId, string $exportType = 'csv')
    {
        $order = Order::query()->findOrFail($orderId);

        $data = $this->prepareData($order);

        if ($exportType === 'xlsx') {
            return $this->exportXlsx($orderId, $data);
        }

        return $this->exportCsv($orderId, $data);
    }

    private function prepareData(Order $order): array
    {
        $data = [];

        $order->suborders()->each(function (SubOrder $subOrder) use (&$data) {
            $subOrder->items->each(function (OrderItem $orderItem) use (&$data, $subOrder) {
                $data[] = [
                    'date' => $orderItem->order->created_at->format('Y-m-d'),
                    'order_number' => $orderItem->order->order_number,
                    'product_name' => $orderItem->productOffer->name,
                    'product_sku' => $orderItem->productOffer->vendor_sku,
                    'vendor' => $orderItem->vendor->name,
                    'vendor_order_number' => $subOrder->external_id ?? 'N/A',
                    'price' => round($orderItem->price / 100, 2),
                    'quantity' => $orderItem->quantity,
                    'total_price' => round($orderItem->total_price / 100, 2),
                ];
            });
        });

        return $data;
    }

    private function exportCsv(string $orderId, array $data)
    {
        $columns = [
            'date',
            'order_number',
            'product_name',
            'product_sku',
            'vendor',
            'vendor_order_number',
            'price',
            'quantity',
            'total_price',
        ];

        $csvFileName = "{$orderId}-checklist.csv";
        $csvFile = tmpfile();
        fputcsv($csvFile, $columns);

        foreach ($data as $row) {
            fputcsv($csvFile, array_values($row));
        }

        Storage::disk('local')->put($csvFileName, $csvFile);
        fclose($csvFile);

        return Response::download(Storage::path($csvFileName), $csvFileName)->deleteFileAfterSend(true);
    }

    private function exportXlsx(string $orderId, array $data)
    {
        $fileName = "{$orderId}-checklist.xlsx";
        $filePath = Storage::path($fileName);

        (new FastExcel(collect($data)))->export($filePath);

        return Response::download($filePath, $fileName)->deleteFileAfterSend(true);
    }
}
