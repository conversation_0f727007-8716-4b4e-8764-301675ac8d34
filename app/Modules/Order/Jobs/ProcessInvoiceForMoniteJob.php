<?php

declare(strict_types=1);

namespace App\Modules\Order\Jobs;

use App\Modules\Monite\Services\MonitePayableService;
use App\Modules\Order\Models\ExternalOrder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

final class ProcessInvoiceForMoniteJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        private readonly ExternalOrder $externalOrder
    ) {}

    /**
     * Execute the job.
     */
    public function handle(MonitePayableService $payableService): void
    {
        Log::info('Processing invoice for Monite payable', [
            'external_order_id' => $this->externalOrder->id,
        ]);

        $payableService->processInvoice($this->externalOrder);
    }
}
