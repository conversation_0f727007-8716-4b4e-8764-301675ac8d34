<?php

declare(strict_types=1);

namespace App\Modules\Clinic\Observers;

use App\Models\Clinic;
use App\Modules\Monite\Jobs\CreateOrUpdateMoniteEntityJob;
use App\Modules\Monite\Jobs\DeleteMoniteEntityJob;

final class ClinicObserver
{
    /**
     * Handle the Clinic "created" event.
     */
    public function created(Clinic $clinic): void
    {
        CreateOrUpdateMoniteEntityJob::dispatch($clinic);
    }

    /**
     * Handle the Clinic "updated" event.
     */
    public function updated(Clinic $clinic): void
    {
        if ($clinic->wasChanged(['name', 'business_tax_id', 'phone_number'])) {
            CreateOrUpdateMoniteEntityJob::dispatch($clinic);
        }
    }

    /**
     * Handle the Clinic "deleted" event.
     */
    public function deleted(Clinic $clinic): void
    {
        // Don't delete Monite entity on soft delete, only on force delete
        // The entity should remain in Monite when clinic is soft deleted
    }

    /**
     * Handle the Clinic "restored" event.
     */
    public function restored(Clinic $clinic): void
    {
        // No action needed - Monite entity was preserved during soft delete
    }

    /**
     * Handle the Clinic "force deleted" event.
     */
    public function forceDeleted(Clinic $clinic): void
    {
        DeleteMoniteEntityJob::dispatch($clinic);
    }
}
