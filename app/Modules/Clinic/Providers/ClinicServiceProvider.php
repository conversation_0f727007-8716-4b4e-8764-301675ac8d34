<?php

declare(strict_types=1);

namespace App\Modules\Clinic\Providers;

use App\Models\Clinic;
use App\Modules\Clinic\Observers\ClinicObserver;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\ServiceProvider;

final class ClinicServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->registerRoutes();

        $this->registerRequestMacros();

        Clinic::observe(ClinicObserver::class);
    }

    protected function registerRoutes(): void
    {
        $this->loadRoutesFrom(__DIR__.'/../Http/routes.php');
    }

    private function registerRequestMacros(): void
    {
        Request::macro('clinicId', fn () => Request::header('Highfive-Clinic') ?? Request::route('clinic')?->id);

        Request::macro('clinic', function (): Clinic {
            /** @var Request $this */
            $clinicId = $this->header('highfive-clinic') ?? $this->header('Highfive-Clinic');

            if (! $clinicId) {
                abort(400, 'Clinic ID is required in header');
            }

            $clinic = Clinic::find($clinicId);

            if (! $clinic) {
                abort(404, 'Clinic not found');
            }

            return $clinic;
        });
    }
}
