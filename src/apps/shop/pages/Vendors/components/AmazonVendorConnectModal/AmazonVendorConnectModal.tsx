import {
  type ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';
import { VendorType } from '@/types';
import { Modal } from '@/components';
import styles from './AmazonVendorConnectModal.module.css';
import { MODAL_NAME } from '@/constants';
import { Logo } from '@/libs/ui/Logo/Logo';
import { Button } from '@/libs/ui/Button/Button';
import { ProgressBar } from '@/libs/ui/ProgressBar/ProgressBar';
import amazonIconUrl from './amazon-icon.svg';
import { ApplyDiscount } from './components/ApplyDiscount/ApplyDiscount';
import { WatchTutorial } from './components/WatchTutorial/WatchTutorial';
import { LogIntoAmazon } from './components/LogIntoAmazon/LogIntoAmazon';
import { useState } from 'react';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

type VendorConnectModalOptions = ModalOptionProps & {
  vendor: VendorType;
};

export const AmazonVendorConnectModal = () => {
  const { modalOption } = useModalStore();
  const { vendor } = modalOption as VendorConnectModalOptions;
  const [currentStep, setCurrentStep] = useState(1);

  const goToNextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 1:
        return t('client.vendors.amazon.discount');
      case 2:
        return t('client.vendors.amazon.watchConnectionTutorial');
      default:
        return t('client.vendors.amazon.connectAccount');
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return <ApplyDiscount onNext={goToNextStep} />;
      case 2:
        return (
          <WatchTutorial onNext={goToNextStep} onPrevious={goToPreviousStep} />
        );
      default:
        return <LogIntoAmazon onPrevious={goToPreviousStep} />;
    }
  };

  if (!vendor) {
    return null;
  }

  return (
    <Modal
      name={MODAL_NAME.AMAZON_VENDOR_CONNECT}
      size="491px"
      customClasses={{
        header: styles.outerModalHeader,
        body: styles.outerModalBody,
        content: styles.outerModalContent,
      }}
      withCloseButton
    >
      <div className="relative top-[-1.5rem] w-full">
        <div className="flex w-full flex-col items-center">
          <div className="flex">
            <div className="mb-4 rounded border border-black/[0.09] bg-white p-3">
              <Logo type="emblem" className="w-12" />
            </div>
            <span className="mx-4 mt-4 text-3xl">+</span>
            <div className="mb-4 flex items-center justify-center rounded border border-black/[0.09] bg-white p-3">
              <img src={amazonIconUrl} alt="Logo" width="100%" />
            </div>
          </div>
          <p className="mb-2 text-xl font-semibold">
            {t('client.vendors.amazon.connectWithVendor')}
          </p>
          <p className="px-2 text-center text-sm text-black/70">
            <Trans
              i18nKey="client.vendors.amazon.specialOfferDescription"
              components={{
                highlight: <span className="text-black" />,
              }}
            />
          </p>
          <div className="mt-4 mb-5 flex w-full flex-col items-center rounded-lg border border-black/[0.04] bg-[#F2F8FC] p-4">
            <div className="mb-1 flex w-full justify-between">
              <span className="text-sm font-medium">{getStepTitle()}</span>
              <span className="text-xs text-black/70">
                STEP {currentStep}/3
              </span>
            </div>
            <ProgressBar
              values={[
                {
                  value: 3,
                  color: 'bg-[#518EF8]',
                },
                {
                  value: 7,
                  color: 'bg-black/[0.04]',
                },
              ]}
              showLegend={false}
            />
            {renderCurrentStep()}
          </div>
          {currentStep !== 2 && (
            <span className="mb-2 text-[16px]">
              {t('client.vendors.amazon.needHelp')}{' '}
              <Button variant="unstyled" onClick={() => setCurrentStep(2)}>
                <span className="underline">
                  {t('client.vendors.amazon.watchTutorial')}
                </span>
              </Button>
            </span>
          )}
        </div>
      </div>
    </Modal>
  );
};
