import { mergeClasses } from '@/utils/tailwind';

interface ProgressValue {
  value: number;
  color: string;
  label?: string;
}

interface ProgressBarProps {
  values: ProgressValue[];
  title?: string;
  showLegend?: boolean;
  height?: string;
  className?: string;
  rounded?: boolean;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  values,
  title,
  showLegend = true,
  height = 'h-[10px]',
  className = '',
  rounded = false,
}) => {
  const isSinglePercentage = values.length === 1 && values[0].value <= 100;
  const processedValues = isSinglePercentage
    ? [...values, { value: 100 - values[0].value, color: 'bg-black/[0.04]' }]
    : values;

  const totalValue = processedValues.reduce((sum, value) => sum + value.value, 0);

  return (
    <div className={mergeClasses('w-full', rounded && 'rounded-xl', className)}>
      {title && <p className="mb-1 text-xs font-medium text-[#666]">{title}</p>}

      <div
        className={mergeClasses(
          'mb-4 flex items-center justify-center overflow-hidden border border-[#D7D7D7] bg-[#F9F4FF] px-[1px]',
          height
        )}
      >
        {processedValues.map((value, index) => {
          const percentage =
            totalValue > 0 ? (value.value / totalValue) * 100 : 0;

          const isFirst = index === 0;
          const isLast = index === processedValues.length - 1;

          const getBorderRadius = () => {
            if (isFirst && isLast) return 'rounded-lg';
            if (isSinglePercentage && rounded) {
              return isFirst ? 'rounded-l-lg' : isLast ? 'rounded-r-lg' : '';
            }
            if (isFirst) return 'rounded-l-lg';
            if (isLast) return 'rounded-r-lg';
            return '';
          };

          return (
            <div
              key={index}
              className={mergeClasses(
                'h-[6px] transition-all duration-300',
                value.color.startsWith('bg-') && value.color,
                getBorderRadius()
              )}
              style={{
                width: `${percentage}%`,
                ...(value.color.startsWith('bg-')
                  ? {}
                  : { backgroundColor: value.color }),
              }}
              title={value.label}
            />
          );
        })}
      </div>

      {showLegend && values.length > 0 && (
        <div className="flex flex-wrap gap-4">
          {(isSinglePercentage ? values : processedValues).map((value, index, array) => {
            const percentage = totalValue > 0 ? (value.value / totalValue) * 100 : 0;

            return (
              <div key={index} className="flex items-center gap-2">
                <div
                  className="h-2 w-2 rounded-full"
                  style={{
                    backgroundColor: value.color.startsWith('bg-') ? undefined : value.color,
                  }}
                />
                <span className="text-xs text-[#666]">
                  {value.label} {percentage.toFixed(0)}%
                </span>
                {index < array.length - 1 && (
                  <div className="mx-2 h-5 w-[1px] bg-[#ddd]" />
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};
