<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Gpo\Actions\ExportPlatformUsage;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Gpo\Services\PlatformUsageExportService;
use Illuminate\Support\Carbon;

beforeEach(function () {
    $this->gpoAccount = GpoAccount::factory()->create(['name' => 'Test GPO Account']);

    $this->clinicAccount1 = ClinicAccount::factory()->create([
        'gpo_account_id' => $this->gpoAccount->id,
    ]);

    $this->clinic1 = Clinic::factory()->create([
        'clinic_account_id' => $this->clinicAccount1->id,
        'name' => 'Test Clinic 1',
        'created_at' => Carbon::parse('2025-01-10'),
    ]);

    $this->user1 = User::factory()->create([
        'account_id' => $this->clinicAccount1->id,
        'name' => 'Test User 1',
        'email' => '<EMAIL>',
    ]);

    $this->clinic1->users()->attach($this->user1);

    $this->vendor1 = Vendor::factory()->create(['name' => 'Test Vendor 1']);
    $this->productOffer1 = ProductOffer::factory()->create(['vendor_id' => $this->vendor1->id]);

    $this->gpoAccount->recommendedVendors()->attach([
        $this->vendor1->id => ['order' => 1],
    ]);
});

describe('ExportPlatformUsageCsv Action', function () {
    it('exports XLSX with correct headers and content type', function () {
        $action = new ExportPlatformUsage(new PlatformUsageExportService());

        $startDate = Carbon::parse('2025-01-01');
        $endDate = Carbon::parse('2025-01-31');

        $response = $action->handle($this->gpoAccount->id, $startDate, $endDate);

        expect($response)->toBeInstanceOf(Symfony\Component\HttpFoundation\BinaryFileResponse::class);

        $contentDisposition = $response->headers->get('Content-Disposition');
        expect($contentDisposition)->toContain('platform_usage_2025-01-01_to_2025-01-31_');
        expect($contentDisposition)->toContain('.xlsx');
    });
});

describe('PlatformUsageExportService', function () {
    it('generates XLSX with correct data structure', function () {
        $clinics = collect([$this->clinic1]);
        $service = new PlatformUsageExportService();

        $startDate = Carbon::parse('2025-01-01');
        $endDate = Carbon::parse('2025-01-31');

        $response = $service->exportToXlsx($clinics, $startDate, $endDate, 30);

        expect($response)->toBeInstanceOf(Symfony\Component\HttpFoundation\BinaryFileResponse::class);

        $contentDisposition = $response->headers->get('Content-Disposition');
        expect($contentDisposition)->toContain('.xlsx');
    });

    it('handles clinics with no orders correctly', function () {
        $clinics = collect([$this->clinic1]);
        $service = new PlatformUsageExportService();

        $startDate = Carbon::parse('2025-01-01');
        $endDate = Carbon::parse('2025-01-31');

        $response = $service->exportToXlsx($clinics, $startDate, $endDate, 30);

        expect($response)->toBeInstanceOf(Symfony\Component\HttpFoundation\BinaryFileResponse::class);

        // Verify the file exists and has the correct extension
        $file = $response->getFile();
        expect($file->getExtension())->toBe('xlsx');
        expect($file->getSize())->toBeGreaterThan(0);
    });

    it('generates valid XLSX file with orders', function () {
        $order1 = Order::factory()->create([
            'clinic_id' => $this->clinic1->id,
            'user_id' => $this->user1->id,
            'created_at' => Carbon::now()->subDays(5),
        ]);

        OrderItem::factory()->create([
            'order_id' => $order1->id,
            'product_offer_id' => $this->productOffer1->id,
            'quantity' => 1,
            'price' => 10000,
        ]);

        $clinics = collect([$this->clinic1]);
        $service = new PlatformUsageExportService();

        $startDate = Carbon::now()->subDays(10);
        $endDate = Carbon::now();

        $response = $service->exportToXlsx($clinics, $startDate, $endDate, 30);

        expect($response)->toBeInstanceOf(Symfony\Component\HttpFoundation\BinaryFileResponse::class);

        // Verify the file exists and has the correct extension
        $file = $response->getFile();
        expect($file->getExtension())->toBe('xlsx');
        expect($file->getSize())->toBeGreaterThan(0);
    });
});
