<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Modules\Monite\Actions\CreatePayableFromInvoiceAction;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Order\Models\ExternalOrder;
use App\Modules\Order\Services\Vendor\Contracts\InvoiceSynchronizer;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

beforeEach(function () {
    // Mock the MoniteApiClientInterface to avoid actual API calls
    $this->mock = mock(MoniteApiClientInterface::class);
    $this->app->instance(MoniteApiClientInterface::class, $this->mock);
});

test('creates a payable from an invoice', function () {
    // Arrange
    $clinic = Clinic::factory()->create(['monite_entity_id' => 'test-monite-entity-id']);

    $externalOrder = ExternalOrder::factory()->create([
        'invoice_file_path' => 'test/invoice.pdf',
        'monite_payable_id' => null,
    ]);

    // Mock storage to return a test file content
    Storage::fake(InvoiceSynchronizer::INVOICE_STORAGE_DISK);
    Storage::disk(InvoiceSynchronizer::INVOICE_STORAGE_DISK)->put('test/invoice.pdf', 'test invoice content');

    // Generate a valid UUID for the test
    $payableId = Str::uuid()->toString();

    // Mock response for the API client
    $mockResponse = new Illuminate\Http\Client\Response(
        new GuzzleHttp\Psr7\Response(200, [], json_encode([
            'id' => $payableId,
            'entity_id' => $clinic->monite_entity_id,
            'status' => 'draft',
        ]))
    );

    // Mock the client methods
    $this->mock->shouldReceive('withEntityId')
        ->once()
        ->with($clinic->monite_entity_id)
        ->andReturnSelf();

    $this->mock->shouldReceive('uploadFile')
        ->once()
        ->andReturn($mockResponse);

    // Act
    $action = app(CreatePayableFromInvoiceAction::class);
    $payableId = $action->execute($externalOrder, $clinic);

    // Assert
    expect($payableId)->toBe($payableId);
    $externalOrder->refresh();
    expect($externalOrder->monite_payable_id)->toBe($payableId);
});

test('returns null if invoice file path is empty', function () {
    // Arrange
    $clinic = Clinic::factory()->create(['monite_entity_id' => 'test-monite-entity-id']);
    $externalOrder = ExternalOrder::factory()->create(['invoice_file_path' => null]);

    // Act
    $action = app(CreatePayableFromInvoiceAction::class);
    $payableId = $action->execute($externalOrder, $clinic);

    // Assert
    expect($payableId)->toBeNull();
    $externalOrder->refresh();
    expect($externalOrder->monite_payable_id)->toBeNull();
});

test('returns existing payable ID if already processed', function () {
    // Arrange
    $clinic = Clinic::factory()->create(['monite_entity_id' => 'test-monite-entity-id']);
    $existingPayableId = Str::uuid()->toString();
    $externalOrder = ExternalOrder::factory()->create([
        'invoice_file_path' => 'test/invoice.pdf',
        'monite_payable_id' => $existingPayableId,
    ]);

    // Act
    $action = app(CreatePayableFromInvoiceAction::class);
    $payableId = $action->execute($externalOrder, $clinic);

    // Assert
    expect($payableId)->toBe($existingPayableId);
    $externalOrder->refresh();
    expect($externalOrder->monite_payable_id)->toBe($existingPayableId);

    // Verify the API was not called
    $this->mock->shouldNotHaveReceived('withEntityId');
    $this->mock->shouldNotHaveReceived('uploadFile');
});

test('returns null if file content cannot be read', function () {
    // Arrange
    $clinic = Clinic::factory()->create(['monite_entity_id' => 'test-monite-entity-id']);
    $externalOrder = ExternalOrder::factory()->create([
        'invoice_file_path' => 'non_existent_file.pdf',
        'monite_payable_id' => null,
    ]);

    Storage::fake(InvoiceSynchronizer::INVOICE_STORAGE_DISK);

    // Act
    $action = app(CreatePayableFromInvoiceAction::class);
    $payableId = $action->execute($externalOrder, $clinic);

    // Assert
    expect($payableId)->toBeNull();
    $externalOrder->refresh();
    expect($externalOrder->monite_payable_id)->toBeNull();

    // Verify the API was not called
    $this->mock->shouldNotHaveReceived('withEntityId');
    $this->mock->shouldNotHaveReceived('uploadFile');
});
