<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Modules\Account\Models\Role;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Models\MoniteRoleMapping;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use App\Modules\Monite\Services\MoniteRoleService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Response;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->clinic = Clinic::factory()->create([
        'monite_entity_id' => 'test-entity-id',
    ]);

    // Enable Monite feature for this clinic
    $moniteFeatureService = app(MoniteFeatureFlagService::class);
    $moniteFeatureService->enable($this->clinic);

    $this->ownerRole = Role::firstOrCreate([
        'name' => 'clinic:owner',
        'guard_name' => 'web',
    ]);
    $this->adminRole = Role::firstOrCreate([
        'name' => 'clinic:admin',
        'guard_name' => 'web',
    ]);
    $this->managerRole = Role::firstOrCreate([
        'name' => 'clinic:manager',
        'guard_name' => 'web',
    ]);
});

describe('MoniteRoleService', function () {

    describe('createOrUpdateRoleForClinic', function () {
        it('creates monite role for clinic role', function () {
            $mockClient = mock(MoniteApiClientInterface::class);
            $mockResponse = mock(Response::class);

            $mockResponse->shouldReceive('successful')->andReturn(true);
            $mockResponse->shouldReceive('json')->andReturn(['id' => 'monite-role-id-123']);

            $mockClient->shouldReceive('withEntityId')
                ->with('test-entity-id')
                ->andReturnSelf();
            $mockClient->shouldReceive('post')
                ->with('/roles', Mockery::on(function ($payload) {
                    // Check that the payload has the expected structure
                    return isset($payload['name']) &&
                           $payload['name'] === 'clinic:owner' &&
                           isset($payload['permissions']) &&
                           isset($payload['permissions']['objects']) &&
                           is_array($payload['permissions']['objects']);
                }))
                ->andReturn($mockResponse);

            $service = new MoniteRoleService($mockClient);
            $moniteRoleId = $service->createOrUpdateRoleForClinic($this->ownerRole, $this->clinic);

            expect($moniteRoleId)->toBe('monite-role-id-123');

            $this->assertDatabaseHas('monite_role_mappings', [
                'role_id' => $this->ownerRole->id,
                'monite_role_id' => 'monite-role-id-123',
                'clinic_id' => $this->clinic->id,
                'entity_id' => 'test-entity-id',
            ]);
        });

        it('updates existing monite role when mapping exists', function () {
            // Create existing mapping
            $mapping = MoniteRoleMapping::factory()->create([
                'role_id' => $this->adminRole->id,
                'monite_role_id' => 'existing-monite-role-id',
                'clinic_id' => $this->clinic->id,
                'entity_id' => 'test-entity-id',
            ]);

            $mockClient = mock(MoniteApiClientInterface::class);
            $mockResponse = mock(Response::class);

            $mockResponse->shouldReceive('successful')->andReturn(true);

            $mockClient->shouldReceive('withEntityId')
                ->with('test-entity-id')
                ->andReturnSelf();
            $mockClient->shouldReceive('patch')
                ->with('/roles/existing-monite-role-id', Mockery::any())
                ->andReturn($mockResponse);

            $service = new MoniteRoleService($mockClient);
            $moniteRoleId = $service->createOrUpdateRoleForClinic($this->adminRole, $this->clinic);

            expect($moniteRoleId)->toBe('existing-monite-role-id');
        });
    });

    describe('syncAllRolesForClinic', function () {
        it('syncs all clinic roles', function () {
            $mockClient = mock(MoniteApiClientInterface::class);

            $mockClient->shouldReceive('withEntityId')
                ->with('test-entity-id')
                ->andReturnSelf()
                ->times(3);

            // Mock responses for each role creation
            foreach (['owner', 'admin', 'manager'] as $index => $roleType) {
                $mockResponse = mock(Response::class);
                $mockResponse->shouldReceive('successful')->andReturn(true);
                $mockResponse->shouldReceive('json')->andReturn(['id' => "monite-{$roleType}-role-id"]);

                $mockClient->shouldReceive('post')
                    ->andReturn($mockResponse);
            }

            $service = new MoniteRoleService($mockClient);
            $service->syncAllRolesForClinic($this->clinic);

            expect(MoniteRoleMapping::where('clinic_id', $this->clinic->id)->count())->toBe(3);
        });

        it('returns early when clinic has no monite entity', function () {
            $clinicWithoutEntity = Clinic::factory()->create([
                'monite_entity_id' => null,
            ]);

            $service = new MoniteRoleService(mock(MoniteApiClientInterface::class));

            // Should not throw exception, just return early
            expect(fn () => $service->syncAllRolesForClinic($clinicWithoutEntity))
                ->not->toThrow(Exception::class);
        });
    });

    describe('deleteRoleForClinic', function () {
        it('deletes monite role when role mapping exists', function () {
            $mapping = MoniteRoleMapping::factory()->create([
                'role_id' => $this->managerRole->id,
                'monite_role_id' => 'monite-role-to-delete',
                'clinic_id' => $this->clinic->id,
                'entity_id' => 'test-entity-id',
            ]);

            $mockClient = mock(MoniteApiClientInterface::class);
            $mockResponse = mock(Response::class);

            $mockResponse->shouldReceive('successful')->andReturn(true);

            $mockClient->shouldReceive('withEntityId')
                ->with('test-entity-id')
                ->andReturnSelf();
            $mockClient->shouldReceive('delete')
                ->with('/roles/monite-role-to-delete')
                ->andReturn($mockResponse);

            $service = new MoniteRoleService($mockClient);
            $service->deleteRoleForClinic($this->managerRole, $this->clinic);

            $this->assertDatabaseMissing('monite_role_mappings', [
                'id' => $mapping->id,
            ]);
        });

        it('does nothing when no mapping exists', function () {
            $mockClient = mock(MoniteApiClientInterface::class);
            $service = new MoniteRoleService($mockClient);

            // Should not throw any exception
            expect(fn () => $service->deleteRoleForClinic($this->managerRole, $this->clinic))
                ->not->toThrow(Exception::class);
        });
    });

    describe('getMoniteRoleId', function () {
        it('returns null when getting monite role id for non-existent mapping', function () {
            $service = new MoniteRoleService(mock(MoniteApiClientInterface::class));
            $moniteRoleId = $service->getMoniteRoleId($this->ownerRole, $this->clinic);

            expect($moniteRoleId)->toBeNull();
        });

        it('returns monite role id when mapping exists', function () {
            MoniteRoleMapping::factory()->create([
                'role_id' => $this->ownerRole->id,
                'monite_role_id' => 'existing-monite-role-id',
                'clinic_id' => $this->clinic->id,
                'entity_id' => 'test-entity-id',
            ]);

            $service = new MoniteRoleService(mock(MoniteApiClientInterface::class));
            $moniteRoleId = $service->getMoniteRoleId($this->ownerRole, $this->clinic);

            expect($moniteRoleId)->toBe('existing-monite-role-id');
        });
    });

    describe('deleteAllRolesForClinic', function () {
        it('deletes all monite roles for a clinic', function () {
            // Create multiple mappings
            $mappings = collect([
                MoniteRoleMapping::factory()->create([
                    'role_id' => $this->ownerRole->id,
                    'monite_role_id' => 'monite-owner-role',
                    'clinic_id' => $this->clinic->id,
                    'entity_id' => 'test-entity-id',
                ]),
                MoniteRoleMapping::factory()->create([
                    'role_id' => $this->adminRole->id,
                    'monite_role_id' => 'monite-admin-role',
                    'clinic_id' => $this->clinic->id,
                    'entity_id' => 'test-entity-id',
                ]),
            ]);

            $mockClient = mock(MoniteApiClientInterface::class);
            $mockResponse = mock(Response::class);

            $mockResponse->shouldReceive('successful')->andReturn(true);

            $mockClient->shouldReceive('withEntityId')
                ->with('test-entity-id')
                ->andReturnSelf()
                ->times(2);

            $mockClient->shouldReceive('delete')
                ->with('/roles/monite-owner-role')
                ->andReturn($mockResponse);

            $mockClient->shouldReceive('delete')
                ->with('/roles/monite-admin-role')
                ->andReturn($mockResponse);

            $service = new MoniteRoleService($mockClient);
            $service->deleteAllRolesForClinic($this->clinic);

            // All mappings should be deleted
            expect(MoniteRoleMapping::where('clinic_id', $this->clinic->id)->count())->toBe(0);
        });
    });
});
