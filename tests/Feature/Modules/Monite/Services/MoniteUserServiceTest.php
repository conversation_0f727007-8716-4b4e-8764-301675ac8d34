<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\User;
use App\Modules\Account\Models\Role;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Models\MoniteRoleMapping;
use App\Modules\Monite\Models\MoniteUserMapping;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use App\Modules\Monite\Services\MoniteUserService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Response;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->clinic = Clinic::factory()->create([
        'monite_entity_id' => 'test-entity-id',
    ]);

    // Enable Monite feature for this clinic
    $moniteFeatureService = app(MoniteFeatureFlagService::class);
    $moniteFeatureService->enable($this->clinic);

    $this->ownerRole = Role::firstOrCreate([
        'name' => 'clinic:owner',
        'guard_name' => 'web',
    ]);
    $this->adminRole = Role::firstOrCreate([
        'name' => 'clinic:admin',
        'guard_name' => 'web',
    ]);
    $this->managerRole = Role::firstOrCreate([
        'name' => 'clinic:manager',
        'guard_name' => 'web',
    ]);
});

describe('MoniteUserService', function () {

    describe('createOrUpdateUserForClinic', function () {
        it('creates monite user for clinic user', function () {
            $mockClient = mock(MoniteApiClientInterface::class);
            $mockResponse = mock(Response::class);

            // Create role and user with a specific name
            $user = User::factory()->create(['name' => 'John Doe']);
            $user->roles()->attach($this->ownerRole);

            // Create role mapping
            MoniteRoleMapping::create([
                'clinic_id' => $this->clinic->id,
                'role_id' => $this->ownerRole->id,
                'monite_role_id' => 'monite-role-123',
                'entity_id' => 'test-entity-id',
            ]);

            // Mock successful API response
            $mockResponse->shouldReceive('successful')->andReturn(true);
            $mockResponse->shouldReceive('json')->andReturn(['id' => 'monite-user-123']);

            $mockClient->shouldReceive('withEntityId')
                ->with('test-entity-id')
                ->andReturnSelf();
            $mockClient->shouldReceive('post')
                ->with('/entity_users', [
                    'login' => $user->email,
                    'first_name' => 'John Doe',
                    'role_id' => 'monite-role-123',
                    'email' => $user->email,
                    'phone' => '',
                ])
                ->andReturn($mockResponse);

            $service = new MoniteUserService($mockClient);
            $result = $service->createOrUpdateUserForClinic($user, $this->clinic);

            expect($result)->toBe('monite-user-123');

            $this->assertDatabaseHas('monite_user_mappings', [
                'user_id' => $user->id,
                'monite_user_id' => 'monite-user-123',
                'clinic_id' => $this->clinic->id,
                'entity_id' => 'test-entity-id',
            ]);
        });

        it('updates existing monite user for clinic user', function () {
            $mockClient = mock(MoniteApiClientInterface::class);
            $mockResponse = mock(Response::class);

            // Create role and user with a specific name
            $user = User::factory()->create(['name' => 'Jane Smith']);
            $user->roles()->attach($this->adminRole);

            // Create existing mapping
            MoniteUserMapping::create([
                'user_id' => $user->id,
                'clinic_id' => $this->clinic->id,
                'monite_user_id' => 'existing-monite-user-123',
                'entity_id' => 'test-entity-id',
            ]);

            // Create role mapping
            MoniteRoleMapping::create([
                'clinic_id' => $this->clinic->id,
                'role_id' => $this->adminRole->id,
                'monite_role_id' => 'monite-role-456',
                'entity_id' => 'test-entity-id',
            ]);

            // Mock successful API response
            $mockResponse->shouldReceive('successful')->andReturn(true);

            $mockClient->shouldReceive('withEntityId')
                ->with('test-entity-id')
                ->andReturnSelf();
            $mockClient->shouldReceive('patch')
                ->with('/entity_users/existing-monite-user-123', [
                    'login' => $user->email,
                    'first_name' => 'Jane Smith',
                    'role_id' => 'monite-role-456',
                    'email' => $user->email,
                    'phone' => '',
                ])
                ->andReturn($mockResponse);

            $service = new MoniteUserService($mockClient);
            $result = $service->createOrUpdateUserForClinic($user, $this->clinic);

            expect($result)->toBe('existing-monite-user-123');
        });
    });

    describe('deleteUserForClinic', function () {
        it('deletes monite user for clinic user', function () {
            $mockClient = mock(MoniteApiClientInterface::class);
            $mockResponse = mock(Response::class);

            // Create user
            $user = User::factory()->create();

            // Create existing mapping
            MoniteUserMapping::create([
                'user_id' => $user->id,
                'clinic_id' => $this->clinic->id,
                'monite_user_id' => 'monite-user-to-delete-123',
                'entity_id' => 'test-entity-id',
            ]);

            // Mock successful API response
            $mockResponse->shouldReceive('successful')->andReturn(true);

            $mockClient->shouldReceive('withEntityId')
                ->with('test-entity-id')
                ->andReturnSelf();
            $mockClient->shouldReceive('delete')
                ->with('/entity_users/monite-user-to-delete-123')
                ->andReturn($mockResponse);

            $service = new MoniteUserService($mockClient);
            $service->deleteUserForClinic($user, $this->clinic);

            // Assert mapping was deleted
            $this->assertDatabaseMissing('monite_user_mappings', [
                'user_id' => $user->id,
                'clinic_id' => $this->clinic->id,
            ]);
        });
    });

    describe('getMoniteUserId', function () {
        it('returns null when getting monite user id for non existing mapping', function () {
            $clinic = Clinic::factory()->create();
            $user = User::factory()->create();

            $service = new MoniteUserService(mock(MoniteApiClientInterface::class));
            $result = $service->getMoniteUserId($user, $clinic);

            expect($result)->toBeNull();
        });

        it('returns monite user id when mapping exists', function () {
            $clinic = Clinic::factory()->create();

            // Enable Monite feature for this clinic
            $moniteFeatureService = app(MoniteFeatureFlagService::class);
            $moniteFeatureService->enable($clinic);

            $user = User::factory()->create();

            MoniteUserMapping::create([
                'user_id' => $user->id,
                'clinic_id' => $clinic->id,
                'monite_user_id' => 'monite-user-123',
                'entity_id' => 'entity-123',
            ]);

            $service = new MoniteUserService(mock(MoniteApiClientInterface::class));
            $result = $service->getMoniteUserId($user, $clinic);

            expect($result)->toBe('monite-user-123');
        });
    });

    describe('syncAllUsersForClinic', function () {
        it('returns early when clinic has no monite entity', function () {
            $clinic = Clinic::factory()->create(['monite_entity_id' => null]);
            $user = User::factory()->create();

            $service = new MoniteUserService(mock(MoniteApiClientInterface::class));

            // Should not throw exception, just return early
            expect(fn () => $service->syncAllUsersForClinic($clinic))
                ->not->toThrow(Exception::class);
        });
    });

    describe('createOrUpdateUserForClinic', function () {
        it('returns early when user has no valid role mapping', function () {
            $clinic = Clinic::factory()->create(['monite_entity_id' => 'entity-123']);
            $user = User::factory()->create();

            $service = new MoniteUserService(mock(MoniteApiClientInterface::class));

            // Should not throw exception, just return early
            expect(fn () => $service->createOrUpdateUserForClinic($user, $clinic))
                ->not->toThrow(Exception::class);
        });
    });
});
