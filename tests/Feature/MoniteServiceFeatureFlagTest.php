<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\User;
use App\Modules\Account\Models\Role;
use App\Modules\Monite\Services\MoniteEntityService;
use App\Modules\Monite\Services\MoniteRoleService;
use App\Modules\Monite\Services\MoniteUserService;
use Laravel\Pennant\Feature;

use function Pest\Laravel\mock;

beforeEach(function () {
    // Mock the MoniteApiClient to avoid real API calls
    $this->mockMoniteApiClient = mock('App\Modules\Monite\Contracts\MoniteApiClientInterface');
    $this->app->instance('App\Modules\Monite\Contracts\MoniteApiClientInterface', $this->mockMoniteApiClient);
});

it('skips monite entity creation when feature is disabled', function () {
    $clinic = Clinic::factory()->create();

    // Ensure feature is disabled
    Feature::for($clinic)->deactivate('monite-integration');

    $service = app(MoniteEntityService::class);
    $result = $service->createEntityForClinic($clinic);

    expect($result)->toBeNull();

    // Verify no API calls were made
    $this->mockMoniteApiClient->shouldNotHaveReceived('post');
});

it('skips monite entity update when feature is disabled', function () {
    $clinic = Clinic::factory()->create();

    // Ensure feature is disabled
    Feature::for($clinic)->deactivate('monite-integration');

    $service = app(MoniteEntityService::class);
    $service->updateEntityForClinic($clinic);

    // Verify no API calls were made
    $this->mockMoniteApiClient->shouldNotHaveReceived('patch');
    $this->mockMoniteApiClient->shouldNotHaveReceived('withEntityId');
});

it('skips monite entity deletion when feature is disabled', function () {
    $clinic = Clinic::factory()->create(['monite_entity_id' => 'test-entity-id']);

    // Ensure feature is disabled
    Feature::for($clinic)->deactivate('monite-integration');

    $service = app(MoniteEntityService::class);
    $service->deleteEntityForClinic($clinic);

    // Verify no API calls were made
    $this->mockMoniteApiClient->shouldNotHaveReceived('delete');
});

it('skips monite user creation when feature is disabled', function () {
    $clinic = Clinic::factory()->create();
    $user = User::factory()->create();

    // Ensure feature is disabled
    Feature::for($clinic)->deactivate('monite-integration');

    $service = app(MoniteUserService::class);
    $result = $service->createOrUpdateUserForClinic($user, $clinic);

    expect($result)->toBeNull();

    // Verify no API calls were made
    $this->mockMoniteApiClient->shouldNotHaveReceived('post');
    $this->mockMoniteApiClient->shouldNotHaveReceived('patch');
    $this->mockMoniteApiClient->shouldNotHaveReceived('withEntityId');
});

it('skips monite user deletion when feature is disabled', function () {
    $clinic = Clinic::factory()->create();
    $user = User::factory()->create();

    // Ensure feature is disabled
    Feature::for($clinic)->deactivate('monite-integration');

    $service = app(MoniteUserService::class);
    $service->deleteUserForClinic($user, $clinic);

    // Verify no API calls were made
    $this->mockMoniteApiClient->shouldNotHaveReceived('delete');
    $this->mockMoniteApiClient->shouldNotHaveReceived('withEntityId');
});

it('returns null when getting monite user id when feature is disabled', function () {
    $clinic = Clinic::factory()->create();
    $user = User::factory()->create();

    // Ensure feature is disabled
    Feature::for($clinic)->deactivate('monite-integration');

    $service = app(MoniteUserService::class);
    $result = $service->getMoniteUserId($user, $clinic);

    expect($result)->toBeNull();
});

it('skips monite user sync when feature is disabled', function () {
    $clinic = Clinic::factory()->create();

    // Ensure feature is disabled
    Feature::for($clinic)->deactivate('monite-integration');

    $service = app(MoniteUserService::class);
    $service->syncAllUsersForClinic($clinic);

    // Verify no API calls were made
    $this->mockMoniteApiClient->shouldNotHaveReceived('post');
    $this->mockMoniteApiClient->shouldNotHaveReceived('withEntityId');
});

it('skips monite role creation when feature is disabled', function () {
    $clinic = Clinic::factory()->create();
    $role = Role::firstOrCreate([
        'name' => 'clinic:owner',
        'guard_name' => 'web',
    ]);

    // Ensure feature is disabled
    Feature::for($clinic)->deactivate('monite-integration');

    $service = app(MoniteRoleService::class);
    $result = $service->createOrUpdateRoleForClinic($role, $clinic);

    expect($result)->toBeNull();

    // Verify no API calls were made
    $this->mockMoniteApiClient->shouldNotHaveReceived('post');
    $this->mockMoniteApiClient->shouldNotHaveReceived('patch');
    $this->mockMoniteApiClient->shouldNotHaveReceived('withEntityId');
});

it('skips monite role deletion when feature is disabled', function () {
    $clinic = Clinic::factory()->create();
    $role = Role::firstOrCreate([
        'name' => 'clinic:admin',
        'guard_name' => 'web',
    ]);

    // Ensure feature is disabled
    Feature::for($clinic)->deactivate('monite-integration');

    $service = app(MoniteRoleService::class);
    $service->deleteRoleForClinic($role, $clinic);

    // Verify no API calls were made
    $this->mockMoniteApiClient->shouldNotHaveReceived('delete');
    $this->mockMoniteApiClient->shouldNotHaveReceived('withEntityId');
});

it('allows monite operations when feature is enabled', function () {
    $clinic = Clinic::factory()->create();

    // Enable the feature
    Feature::for($clinic)->activate('monite-integration');

    // Mock successful API response
    $mockResponse = mock('Illuminate\Http\Client\Response');
    $mockResponse->shouldReceive('successful')->andReturn(true);
    $mockResponse->shouldReceive('json')->andReturn(['id' => 'test-entity-id']);

    $this->mockMoniteApiClient->shouldReceive('post')
        ->once()
        ->with('/entities', Mockery::type('array'))
        ->andReturn($mockResponse);

    $service = app(MoniteEntityService::class);
    $result = $service->createEntityForClinic($clinic);

    expect($result)->toBe('test-entity-id');
});

it('logs feature disabled operations', function () {
    $clinic = Clinic::factory()->create();

    // Ensure feature is disabled
    Feature::for($clinic)->deactivate('monite-integration');

    // Mock the log to capture calls
    Log::spy();

    $service = app(MoniteEntityService::class);
    $service->createEntityForClinic($clinic);

    // Verify that the feature disabled operation was logged
    Log::shouldHaveReceived('info')
        ->once()
        ->with('Monite operation skipped - feature disabled for clinic', [
            'clinic_id' => $clinic->id,
            'clinic_name' => $clinic->name,
            'operation' => 'createEntityForClinic',
        ]);
});
