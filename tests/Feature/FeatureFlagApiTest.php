<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\User;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class);

it('can check monite status for current clinic', function () {
    $user = User::factory()->create();
    $clinic = Clinic::factory()->create();

    $moniteFeatureService = app(MoniteFeatureFlagService::class);
    $moniteFeatureService->enable($clinic);

    Sanctum::actingAs($user);

    $response = $this->postJson('/api/feature-flags/check-monite', [], [
        'Highfive-Clinic' => $clinic->id,
    ]);

    $response->assertOk()
        ->assertJson([
            'clinicId' => $clinic->id,
            'clinicName' => $clinic->name,
            'moniteEnabled' => true,
        ]);
});

it('returns false when monite feature is disabled', function () {
    $user = User::factory()->create();
    $clinic = Clinic::factory()->create();

    $moniteFeatureService = app(MoniteFeatureFlagService::class);
    $moniteFeatureService->disable($clinic);

    Sanctum::actingAs($user);

    $response = $this->postJson('/api/feature-flags/check-monite', [], [
        'Highfive-Clinic' => $clinic->id,
    ]);

    $response->assertOk()
        ->assertJson([
            'clinicId' => $clinic->id,
            'clinicName' => $clinic->name,
            'moniteEnabled' => false,
        ]);
});

it('requires authentication', function () {
    $clinic = Clinic::factory()->create();

    $response = $this->postJson('/api/feature-flags/check-monite', [], [
        'Highfive-Clinic' => $clinic->id,
    ]);

    $response->assertUnauthorized();
});

it('requires clinic header', function () {
    $user = User::factory()->create();
    Sanctum::actingAs($user);

    $response = $this->postJson('/api/feature-flags/check-monite', []);

    $response->assertStatus(400);
});

it('handles non-existent clinic', function () {
    $user = User::factory()->create();
    Sanctum::actingAs($user);

    $response = $this->postJson('/api/feature-flags/check-monite', [], [
        'Highfive-Clinic' => '12345678-1234-1234-1234-123456789012',
    ]);

    $response->assertNotFound();
});
