<?php

declare(strict_types=1);

namespace Database\Factories\Modules\Monite\Models;

use App\Models\Clinic;
use App\Modules\Account\Models\Role;
use App\Modules\Monite\Models\MoniteRoleMapping;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<MoniteRoleMapping>
 */
final class MoniteRoleMappingFactory extends Factory
{
    protected $model = MoniteRoleMapping::class;

    public function definition(): array
    {
        return [
            'role_id' => Role::factory(),
            'monite_role_id' => $this->faker->uuid(),
            'clinic_id' => Clinic::factory(),
            'entity_id' => $this->faker->uuid(),
        ];
    }
}
