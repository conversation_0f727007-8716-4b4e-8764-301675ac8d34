<?php

declare(strict_types=1);

namespace Database\Factories\Modules\Monite\Models;

use App\Models\Clinic;
use App\Models\User;
use App\Modules\Monite\Models\MoniteUserMapping;
use Illuminate\Database\Eloquent\Factories\Factory;

class MoniteUserMappingFactory extends Factory
{
    protected $model = MoniteUserMapping::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'monite_user_id' => $this->faker->uuid(),
            'clinic_id' => Clinic::factory(),
            'entity_id' => $this->faker->uuid(),
        ];
    }

    public function forClinicAndUser(Clinic $clinic, User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'clinic_id' => $clinic->id,
            'user_id' => $user->id,
            'entity_id' => $clinic->monite_entity_id,
        ]);
    }
}
