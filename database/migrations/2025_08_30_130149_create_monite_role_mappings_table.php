<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('monite_role_mappings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('role_id');
            $table->string('monite_role_id');
            $table->uuid('clinic_id');
            $table->string('entity_id');
            $table->timestamps();

            $table->foreign('role_id')->references('id')->on('roles')->onDelete('cascade');
            $table->foreign('clinic_id')->references('id')->on('clinics')->onDelete('cascade');

            $table->unique(['clinic_id', 'role_id'], 'unique_clinic_role_mapping');
            $table->index(['clinic_id', 'role_id']);
            $table->index(['entity_id', 'monite_role_id']);
        });
    }
};
