<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Vendor;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('monite_vendor_mappings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignIdFor(Vendor::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(Clinic::class)->constrained()->cascadeOnDelete();
            $table->uuid('monite_counterpart_id');
            $table->timestamps();

            // Each vendor can only have one mapping per clinic
            $table->unique(['vendor_id', 'clinic_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('monite_vendor_mappings');
    }
};
