<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('shipments', function (Blueprint $table) {
            $table->dropColumn(['eta_date', 'eta_time_range']);

            $table->datetime('eta_date_from')->nullable();
            $table->datetime('eta_date_to')->nullable();
            $table->datetime('date_delivered')->nullable();
        });
    }
};
