<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('monite_user_mappings', function (Blueprint $table) {
            $table->id();
            $table->uuid('user_id'); // Local user ID
            $table->string('monite_user_id'); // Monite user ID
            $table->uuid('clinic_id'); // Clinic ID
            $table->string('entity_id'); // Monite entity ID
            $table->timestamps();

            // Indexes
            $table->index(['user_id', 'clinic_id']);
            $table->index(['clinic_id']);
            $table->index(['monite_user_id']);

            // Foreign keys
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('clinic_id')->references('id')->on('clinics')->onDelete('cascade');
        });
    }
};
