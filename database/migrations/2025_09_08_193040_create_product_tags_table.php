<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('product_tags', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name')->unique();
            $table->timestamps();
        });

        Schema::create('product_product_tag', function (Blueprint $table) {
            $table->foreignUuid('product_id')->constrained('products');
            $table->foreignUuid('product_tag_id')->constrained('product_tags');
            $table->timestamps();
            $table->unique(['product_id', 'product_tag_id']);
        });
    }
};
